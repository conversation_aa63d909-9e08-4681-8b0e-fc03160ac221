-- 批量简历解析性能优化SQL脚本

-- 1. 创建批量任务表
CREATE TABLE IF NOT EXISTS `batch_resume_task` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `task_id` VARCHAR(64) NOT NULL UNIQUE COMMENT '任务ID',
    `status` VARCHAR(20) NOT NULL DEFAULT 'SUBMITTED' COMMENT '任务状态：SUBMITTED,PROCESSING,COMPLETED,FAILED',
    `total_files` INT NOT NULL DEFAULT 0 COMMENT '总文件数',
    `processed_files` INT NOT NULL DEFAULT 0 COMMENT '已处理文件数',
    `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功数量',
    `failure_count` INT NOT NULL DEFAULT 0 COMMENT '失败数量',
    `skipped_count` INT NOT NULL DEFAULT 0 COMMENT '跳过数量',
    `submit_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    `start_time` DATETIME NULL COMMENT '开始处理时间',
    `completion_time` DATETIME NULL COMMENT '完成时间',
    `error_message` TEXT NULL COMMENT '错误信息',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_submit_time` (`submit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批量简历解析任务表';

-- 2. 创建子任务表
CREATE TABLE IF NOT EXISTS `batch_resume_sub_task` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `task_id` VARCHAR(64) NOT NULL COMMENT '主任务ID',
    `sub_task_id` VARCHAR(64) NOT NULL UNIQUE COMMENT '子任务ID',
    `status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '子任务状态',
    `file_count` INT NOT NULL DEFAULT 0 COMMENT '文件数量',
    `processed_count` INT NOT NULL DEFAULT 0 COMMENT '已处理数量',
    `success_count` INT NOT NULL DEFAULT 0 COMMENT '成功数量',
    `failure_count` INT NOT NULL DEFAULT 0 COMMENT '失败数量',
    `start_time` DATETIME NULL COMMENT '开始时间',
    `completion_time` DATETIME NULL COMMENT '完成时间',
    `worker_instance` VARCHAR(100) NULL COMMENT '处理实例',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_sub_task_id` (`sub_task_id`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批量简历解析子任务表';

-- 3. 优化现有表索引
-- 用户基础表索引优化
ALTER TABLE `user_base` 
ADD INDEX `idx_phone_email` (`phone`, `email`),
ADD INDEX `idx_created_at` (`created_at`),
ADD INDEX `idx_status` (`status`);

-- 简历解析记录表索引优化
ALTER TABLE `resume_parse_record` 
ADD INDEX `idx_user_id_created` (`user_id`, `created_at`),
ADD INDEX `idx_file_hash` (`file_hash`),
ADD INDEX `idx_parse_status` (`parse_status`);

-- 用户资料表索引优化
ALTER TABLE `user_profile` 
ADD INDEX `idx_user_id` (`user_id`),
ADD INDEX `idx_updated_at` (`updated_at`);

-- 4. 创建性能监控表
CREATE TABLE IF NOT EXISTS `batch_performance_log` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `task_id` VARCHAR(64) NOT NULL COMMENT '任务ID',
    `metric_name` VARCHAR(100) NOT NULL COMMENT '指标名称',
    `metric_value` DECIMAL(15,4) NOT NULL COMMENT '指标值',
    `metric_unit` VARCHAR(20) NULL COMMENT '指标单位',
    `tags` JSON NULL COMMENT '标签信息',
    `timestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
    INDEX `idx_task_metric` (`task_id`, `metric_name`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='批量处理性能日志表';

-- 5. 创建文件处理记录表
CREATE TABLE IF NOT EXISTS `file_process_record` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `task_id` VARCHAR(64) NOT NULL COMMENT '任务ID',
    `file_name` VARCHAR(255) NOT NULL COMMENT '文件名',
    `file_size` BIGINT NOT NULL DEFAULT 0 COMMENT '文件大小',
    `file_hash` VARCHAR(64) NULL COMMENT '文件哈希',
    `status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '处理状态',
    `user_id` BIGINT NULL COMMENT '创建的用户ID',
    `parse_record_id` BIGINT NULL COMMENT '解析记录ID',
    `processing_time_ms` BIGINT NULL COMMENT '处理耗时(毫秒)',
    `error_message` TEXT NULL COMMENT '错误信息',
    `error_type` VARCHAR(50) NULL COMMENT '错误类型',
    `start_time` DATETIME NULL COMMENT '开始处理时间',
    `completion_time` DATETIME NULL COMMENT '完成时间',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_task_id` (`task_id`),
    INDEX `idx_file_hash` (`file_hash`),
    INDEX `idx_status` (`status`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件处理记录表';

-- 6. 数据库连接池优化配置建议
-- 在application.yml中添加以下配置：
/*
spring:
  datasource:
    hikari:
      maximum-pool-size: 50
      minimum-idle: 10
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
      pool-name: BatchResumeHikariCP
      connection-test-query: SELECT 1
      validation-timeout: 5000
*/

-- 7. 创建分区表（适用于大数据量场景）
-- 按月分区的性能日志表
CREATE TABLE IF NOT EXISTS `batch_performance_log_partitioned` (
    `id` BIGINT AUTO_INCREMENT,
    `task_id` VARCHAR(64) NOT NULL,
    `metric_name` VARCHAR(100) NOT NULL,
    `metric_value` DECIMAL(15,4) NOT NULL,
    `metric_unit` VARCHAR(20) NULL,
    `tags` JSON NULL,
    `timestamp` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`, `timestamp`),
    INDEX `idx_task_metric` (`task_id`, `metric_name`),
    INDEX `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY RANGE (YEAR(`timestamp`) * 100 + MONTH(`timestamp`)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 8. 清理历史数据的存储过程
DELIMITER //
CREATE PROCEDURE CleanupBatchProcessingData(IN days_to_keep INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理过期的批量任务记录
    DELETE FROM batch_resume_task 
    WHERE completion_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND status IN ('COMPLETED', 'FAILED');
    
    -- 清理过期的性能日志
    DELETE FROM batch_performance_log 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 清理过期的文件处理记录
    DELETE FROM file_process_record 
    WHERE completion_time < DATE_SUB(NOW(), INTERVAL days_to_keep DAY)
    AND status IN ('COMPLETED', 'FAILED');
    
    COMMIT;
    
    SELECT CONCAT('清理完成，保留最近 ', days_to_keep, ' 天的数据') AS result;
END //
DELIMITER ;

-- 9. 性能监控视图
CREATE VIEW batch_processing_stats AS
SELECT 
    DATE(submit_time) as processing_date,
    COUNT(*) as total_tasks,
    SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_tasks,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_tasks,
    SUM(total_files) as total_files_processed,
    SUM(success_count) as total_success_files,
    SUM(failure_count) as total_failure_files,
    AVG(TIMESTAMPDIFF(SECOND, start_time, completion_time)) as avg_processing_time_seconds,
    MAX(total_files) as max_batch_size,
    MIN(total_files) as min_batch_size
FROM batch_resume_task 
WHERE submit_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(submit_time)
ORDER BY processing_date DESC;

-- 10. 创建定时清理事件
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS cleanup_batch_data
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURRENT_DATE + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CleanupBatchProcessingData(30); -- 保留30天数据
