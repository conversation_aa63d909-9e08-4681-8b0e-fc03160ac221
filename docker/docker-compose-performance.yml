version: '3.8'

services:
  # 用户中心应用 - 多实例部署
  user-center-app-1:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: user-center-app-1
    environment:
      - SPRING_PROFILES_ACTIVE=performance
      - SERVER_PORT=8080
      - INSTANCE_ID=app-1
      - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
      # 阿里云OSS环境变量 (建议通过Docker secrets或外部配置管理)
      - ALIBABA_CLOUD_ACCESS_KEY_ID=${ALIBABA_CLOUD_ACCESS_KEY_ID}
      - ALIBABA_CLOUD_ACCESS_KEY_SECRET=${ALIBABA_CLOUD_ACCESS_KEY_SECRET}
      - ALIYUN_OSS_ENDPOINT=${ALIYUN_OSS_ENDPOINT:-https://oss-cn-hangzhou.aliyuncs.com}
      - ALIYUN_OSS_BUCKET_NAME=${ALIYUN_OSS_BUCKET_NAME:-user-center-files}
    ports:
      - "8080:8080"
    volumes:
      - ../logs:/app/logs
      - /tmp/batch-upload:/tmp/batch-upload
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - user-center-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  user-center-app-2:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: user-center-app-2
    environment:
      - SPRING_PROFILES_ACTIVE=performance
      - SERVER_PORT=8080
      - INSTANCE_ID=app-2
      - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
      # 阿里云OSS环境变量
      - ALIBABA_CLOUD_ACCESS_KEY_ID=${ALIBABA_CLOUD_ACCESS_KEY_ID}
      - ALIBABA_CLOUD_ACCESS_KEY_SECRET=${ALIBABA_CLOUD_ACCESS_KEY_SECRET}
      - ALIYUN_OSS_ENDPOINT=${ALIYUN_OSS_ENDPOINT:-https://oss-cn-hangzhou.aliyuncs.com}
      - ALIYUN_OSS_BUCKET_NAME=${ALIYUN_OSS_BUCKET_NAME:-user-center-files}
    ports:
      - "8081:8080"
    volumes:
      - ../logs:/app/logs
      - /tmp/batch-upload:/tmp/batch-upload
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - user-center-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MySQL数据库 - 性能优化配置
  mysql:
    image: mysql:8.0
    container_name: user-center-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: user_center
      MYSQL_USER: user_center
      MYSQL_PASSWORD: user_center123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=2G
      --innodb-log-file-size=256M
      --innodb-flush-log-at-trx-commit=2
      --innodb-flush-method=O_DIRECT
      --max-connections=500
      --thread-cache-size=50
      --query-cache-size=128M
      --query-cache-type=1
      --slow-query-log=1
      --slow-query-log-file=/var/lib/mysql/slow.log
      --long-query-time=2
    networks:
      - user-center-network
    restart: unless-stopped

  # Redis缓存 - 集群配置
  redis:
    image: redis:7-alpine
    container_name: user-center-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - user-center-network
    restart: unless-stopped

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: user-center-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
      RABBITMQ_DEFAULT_VHOST: /
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./rabbitmq/definitions.json:/etc/rabbitmq/definitions.json
    networks:
      - user-center-network
    restart: unless-stopped

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: user-center-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - user-center-network
    restart: unless-stopped

  # Nginx负载均衡
  nginx:
    image: nginx:alpine
    container_name: user-center-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - user-center-app-1
      - user-center-app-2
    networks:
      - user-center-network
    restart: unless-stopped

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: user-center-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - user-center-network
    restart: unless-stopped

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: user-center-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - user-center-network
    restart: unless-stopped

  # Elasticsearch日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: user-center-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - user-center-network
    restart: unless-stopped

  # Kibana日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    container_name: user-center-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    depends_on:
      - elasticsearch
    networks:
      - user-center-network
    restart: unless-stopped

  # Logstash日志处理
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    container_name: user-center-logstash
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline
      - ./logstash/config:/usr/share/logstash/config
      - ../logs:/app/logs:ro
    depends_on:
      - elasticsearch
    networks:
      - user-center-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
  minio_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  user-center-network:
    driver: bridge
