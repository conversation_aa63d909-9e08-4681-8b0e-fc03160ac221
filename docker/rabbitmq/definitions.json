{"rabbit_version": "3.12.0", "rabbitmq_version": "3.12.0", "product_name": "RabbitMQ", "product_version": "3.12.0", "users": [{"name": "admin", "password_hash": "8+01HURqKdOLgKcbNNFWGA==", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "admin", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "cluster_name", "value": "user-center-cluster"}], "policies": [{"vhost": "/", "name": "ha-all", "pattern": ".*", "apply-to": "all", "definition": {"ha-mode": "all", "ha-sync-mode": "automatic"}, "priority": 0}, {"vhost": "/", "name": "resume-parse-policy", "pattern": "resume\\.parse\\..*", "apply-to": "queues", "definition": {"max-length": 10000, "overflow": "reject-publish", "message-ttl": 3600000}, "priority": 1}], "queues": [{"name": "resume.parse.queue", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-max-length": 10000, "x-dead-letter-exchange": "resume.parse.dlx.exchange", "x-dead-letter-routing-key": "resume.parse.dlq"}}, {"name": "resume.parse.priority.queue", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {"x-max-priority": 10, "x-message-ttl": 1800000, "x-max-length": 5000, "x-dead-letter-exchange": "resume.parse.dlx.exchange", "x-dead-letter-routing-key": "resume.parse.dlq"}}, {"name": "resume.parse.dlq", "vhost": "/", "durable": true, "auto_delete": false, "arguments": {}}], "exchanges": [{"name": "resume.parse.exchange", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "resume.parse.dlx.exchange", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": [{"source": "resume.parse.exchange", "vhost": "/", "destination": "resume.parse.queue", "destination_type": "queue", "routing_key": "resume.parse.batch", "arguments": {}}, {"source": "resume.parse.exchange", "vhost": "/", "destination": "resume.parse.priority.queue", "destination_type": "queue", "routing_key": "resume.parse.priority", "arguments": {}}, {"source": "resume.parse.dlx.exchange", "vhost": "/", "destination": "resume.parse.dlq", "destination_type": "queue", "routing_key": "resume.parse.dlq", "arguments": {}}]}