# RabbitMQ配置文件
# 用于批量简历解析系统的优化配置

## 网络配置
listeners.tcp.default = 5672
management.tcp.port = 15672

## 内存和磁盘配置
# 内存高水位阈值 (40% of available memory)
vm_memory_high_watermark.relative = 0.4

# 磁盘空间低水位阈值
disk_free_limit.relative = 2.0

## 连接配置
# 最大连接数
num_acceptors.tcp = 10

# 心跳间隔 (秒)
heartbeat = 60

# 连接超时
handshake_timeout = 10000

## 通道配置
# 每个连接的最大通道数
channel_max = 2047

## 队列配置
# 默认队列类型
default_queue_type = classic

# 队列主节点定位策略
queue_master_locator = min-masters

## 消息配置
# 最大消息大小 (50MB)
max_message_size = 52428800

# 消息TTL (1小时)
default_message_ttl = 3600000

## 集群配置
# 集群分区处理策略
cluster_partition_handling = autoheal

# 集群节点类型
cluster_formation.peer_discovery_backend = rabbit_peer_discovery_classic_config

## 日志配置
log.console = true
log.console.level = info
log.file = true
log.file.level = info
log.file.rotation.date = $D0
log.file.rotation.size = 10485760

## 管理插件配置
management.rates_mode = basic

# 管理界面刷新间隔
management.sample_retention_policies.global.minute = 5
management.sample_retention_policies.global.hour = 60
management.sample_retention_policies.global.day = 1440

## 性能优化
# 预取计数
consumer_prefetch_count = 10

# TCP缓冲区大小
tcp_listen_options.backlog = 128
tcp_listen_options.nodelay = true
tcp_listen_options.linger.on = true
tcp_listen_options.linger.timeout = 0

# 收集统计信息的间隔
collect_statistics_interval = 5000

## 插件配置
# 启用的插件
plugins.directories.1 = /opt/rabbitmq/plugins
plugins.expand_criteria.1 = rabbitmq_management
plugins.expand_criteria.2 = rabbitmq_prometheus

## 安全配置
# 默认用户
default_user = admin
default_pass = admin123

# 默认虚拟主机
default_vhost = /

# 默认权限
default_permissions.configure = .*
default_permissions.read = .*
default_permissions.write = .*

## 高可用配置
# 镜像队列策略
ha_mode = all
ha_sync_mode = automatic

## 监控配置
# Prometheus指标
prometheus.tcp.port = 15692
prometheus.path = /metrics

## 批量处理优化
# 批量确认
confirm_publish_batch_size = 100

# 批量投递
deliver_batch_size = 100

# 队列索引嵌入阈值
queue_index_embed_msgs_below = 4096
