global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 用户中心应用监控
  - job_name: 'user-center-app'
    metrics_path: '/actuator/prometheus'
    scrape_interval: 10s
    static_configs:
      - targets: 
        - 'user-center-app-1:8080'
        - 'user-center-app-2:8080'
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: host
        replacement: '${1}'

  # MySQL监控
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # RabbitMQ监控
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  # 系统监控
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # 批量处理专项监控
  - job_name: 'batch-processing'
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s  # 更频繁的采集
    static_configs:
      - targets: 
        - 'user-center-app-1:8080'
        - 'user-center-app-2:8080'
    metric_relabel_configs:
      # 只保留批量处理相关指标
      - source_labels: [__name__]
        regex: 'batch_.*|file_.*|third_party_.*'
        action: keep

  # JVM监控
  - job_name: 'jvm-metrics'
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
    static_configs:
      - targets: 
        - 'user-center-app-1:8080'
        - 'user-center-app-2:8080'
    metric_relabel_configs:
      # 只保留JVM相关指标
      - source_labels: [__name__]
        regex: 'jvm_.*|process_.*|system_.*'
        action: keep
