#!/usr/bin/env python3
"""
RabbitMQ消息队列测试工具
用于测试批量简历解析系统的消息生产和消费性能
"""

import pika
import json
import time
import uuid
import threading
from datetime import datetime
from typing import List, Dict, Any
import argparse

class RabbitMQTester:
    """RabbitMQ测试类"""
    
    def __init__(self, host='localhost', port=5672, username='admin', password='admin123'):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.connection = None
        self.channel = None
        
    def connect(self):
        """建立连接"""
        credentials = pika.PlainCredentials(self.username, self.password)
        parameters = pika.ConnectionParameters(
            host=self.host,
            port=self.port,
            credentials=credentials,
            heartbeat=600,
            blocked_connection_timeout=300
        )
        
        self.connection = pika.BlockingConnection(parameters)
        self.channel = self.connection.channel()
        print(f"✅ 连接到RabbitMQ: {self.host}:{self.port}")
        
    def disconnect(self):
        """断开连接"""
        if self.connection and not self.connection.is_closed:
            self.connection.close()
            print("🔌 RabbitMQ连接已断开")
    
    def create_test_message(self, task_id: str, sub_task_id: str, file_count: int) -> Dict[str, Any]:
        """创建测试消息"""
        return {
            "taskId": task_id,
            "subTaskId": sub_task_id,
            "fileUrls": [f"oss://test-bucket/file_{i}.pdf" for i in range(file_count)],
            "parseOptions": {
                "overwriteExisting": False,
                "parseBasicInfo": True,
                "parseContactInfo": True,
                "parseEducation": True,
                "parseWorkExperience": True,
                "parseProjectExperience": True,
                "parseSkills": True,
                "maxConcurrency": 10,
                "fileTimeoutSeconds": 60
            },
            "priority": 5,
            "highPriority": False,
            "createTime": datetime.now().isoformat(),
            "timeoutSeconds": 3600,
            "maxRetries": 3,
            "metadata": {
                "testMessage": True,
                "createdBy": "rabbitmq_test.py"
            }
        }
    
    def send_test_messages(self, count: int, queue_type: str = "normal"):
        """发送测试消息"""
        if not self.channel:
            self.connect()
            
        exchange = "resume.parse.exchange"
        routing_key = "resume.parse.batch" if queue_type == "normal" else "resume.parse.priority"
        
        print(f"📤 开始发送 {count} 条测试消息到 {queue_type} 队列...")
        
        success_count = 0
        start_time = time.time()
        
        for i in range(count):
            try:
                task_id = f"test_{uuid.uuid4().hex[:8]}"
                sub_task_id = f"{task_id}_sub_1"
                
                message = self.create_test_message(task_id, sub_task_id, 5)
                
                # 设置消息属性
                properties = pika.BasicProperties(
                    message_id=str(uuid.uuid4()),
                    timestamp=int(time.time()),
                    delivery_mode=2,  # 持久化
                    headers={
                        "taskId": task_id,
                        "subTaskId": sub_task_id,
                        "retryCount": 0
                    }
                )
                
                if queue_type == "priority":
                    properties.priority = 8
                
                # 发送消息
                self.channel.basic_publish(
                    exchange=exchange,
                    routing_key=routing_key,
                    body=json.dumps(message),
                    properties=properties
                )
                
                success_count += 1
                
                if (i + 1) % 100 == 0:
                    print(f"📊 已发送 {i + 1}/{count} 条消息")
                    
            except Exception as e:
                print(f"❌ 发送消息失败 {i+1}: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 消息发送完成:")
        print(f"   - 总数量: {count}")
        print(f"   - 成功: {success_count}")
        print(f"   - 失败: {count - success_count}")
        print(f"   - 耗时: {duration:.2f}秒")
        print(f"   - 速率: {success_count/duration:.2f} 消息/秒")
    
    def get_queue_info(self, queue_name: str) -> Dict[str, Any]:
        """获取队列信息"""
        try:
            method = self.channel.queue_declare(queue=queue_name, passive=True)
            return {
                "queue": queue_name,
                "message_count": method.method.message_count,
                "consumer_count": method.method.consumer_count
            }
        except Exception as e:
            return {
                "queue": queue_name,
                "error": str(e)
            }
    
    def monitor_queues(self, interval: int = 5, duration: int = 60):
        """监控队列状态"""
        if not self.channel:
            self.connect()
            
        queues = [
            "resume.parse.queue",
            "resume.parse.priority.queue", 
            "resume.parse.dlq"
        ]
        
        print(f"📊 开始监控队列状态 (间隔: {interval}秒, 持续: {duration}秒)...")
        print("-" * 80)
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')}")
            
            for queue_name in queues:
                info = self.get_queue_info(queue_name)
                if "error" in info:
                    print(f"❌ {queue_name}: {info['error']}")
                else:
                    print(f"📋 {queue_name}: {info['message_count']} 消息, {info['consumer_count']} 消费者")
            
            time.sleep(interval)
        
        print("\n✅ 队列监控结束")
    
    def purge_queue(self, queue_name: str):
        """清空队列"""
        if not self.channel:
            self.connect()
            
        try:
            result = self.channel.queue_purge(queue=queue_name)
            print(f"🗑️ 队列 {queue_name} 已清空，删除了 {result.method.message_count} 条消息")
        except Exception as e:
            print(f"❌ 清空队列失败: {e}")
    
    def consume_messages(self, queue_name: str, max_messages: int = 10):
        """消费消息（测试用）"""
        if not self.channel:
            self.connect()
            
        print(f"📥 开始消费队列 {queue_name} 的消息 (最多 {max_messages} 条)...")
        
        consumed_count = 0
        
        def callback(ch, method, properties, body):
            nonlocal consumed_count
            try:
                message = json.loads(body)
                print(f"📨 收到消息 {consumed_count + 1}:")
                print(f"   - 任务ID: {message.get('taskId')}")
                print(f"   - 子任务ID: {message.get('subTaskId')}")
                print(f"   - 文件数量: {len(message.get('fileUrls', []))}")
                print(f"   - 优先级: {properties.priority if properties.priority else 'N/A'}")
                
                # 确认消息
                ch.basic_ack(delivery_tag=method.delivery_tag)
                consumed_count += 1
                
                if consumed_count >= max_messages:
                    ch.stop_consuming()
                    
            except Exception as e:
                print(f"❌ 处理消息失败: {e}")
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
        
        # 设置消费者
        self.channel.basic_qos(prefetch_count=1)
        self.channel.basic_consume(queue=queue_name, on_message_callback=callback)
        
        try:
            self.channel.start_consuming()
        except KeyboardInterrupt:
            print("\n⏹️ 停止消费消息")
            self.channel.stop_consuming()
        
        print(f"✅ 消费完成，共处理 {consumed_count} 条消息")

def main():
    parser = argparse.ArgumentParser(description='RabbitMQ消息队列测试工具')
    parser.add_argument('--host', default='localhost', help='RabbitMQ主机地址')
    parser.add_argument('--port', type=int, default=5672, help='RabbitMQ端口')
    parser.add_argument('--username', default='admin', help='用户名')
    parser.add_argument('--password', default='admin123', help='密码')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 发送消息命令
    send_parser = subparsers.add_parser('send', help='发送测试消息')
    send_parser.add_argument('--count', type=int, default=10, help='消息数量')
    send_parser.add_argument('--type', choices=['normal', 'priority'], default='normal', help='队列类型')
    
    # 监控命令
    monitor_parser = subparsers.add_parser('monitor', help='监控队列状态')
    monitor_parser.add_argument('--interval', type=int, default=5, help='监控间隔(秒)')
    monitor_parser.add_argument('--duration', type=int, default=60, help='监控持续时间(秒)')
    
    # 清空队列命令
    purge_parser = subparsers.add_parser('purge', help='清空队列')
    purge_parser.add_argument('queue', help='队列名称')
    
    # 消费消息命令
    consume_parser = subparsers.add_parser('consume', help='消费消息')
    consume_parser.add_argument('queue', help='队列名称')
    consume_parser.add_argument('--max', type=int, default=10, help='最大消费数量')
    
    # 队列信息命令
    info_parser = subparsers.add_parser('info', help='获取队列信息')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 创建测试器
    tester = RabbitMQTester(args.host, args.port, args.username, args.password)
    
    try:
        if args.command == 'send':
            tester.send_test_messages(args.count, args.type)
        elif args.command == 'monitor':
            tester.monitor_queues(args.interval, args.duration)
        elif args.command == 'purge':
            tester.purge_queue(args.queue)
        elif args.command == 'consume':
            tester.consume_messages(args.queue, args.max)
        elif args.command == 'info':
            tester.connect()
            queues = ["resume.parse.queue", "resume.parse.priority.queue", "resume.parse.dlq"]
            for queue in queues:
                info = tester.get_queue_info(queue)
                print(f"📋 {queue}: {info}")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"❌ 操作失败: {e}")
    finally:
        tester.disconnect()

if __name__ == "__main__":
    main()
