#!/usr/bin/env python3
"""
批量简历解析性能测试脚本
用于测试系统的并发处理能力和性能指标
"""

import asyncio
import aiohttp
import time
import json
import os
import random
import string
from typing import List, Dict, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import argparse

@dataclass
class TestConfig:
    """测试配置"""
    base_url: str = "http://localhost"
    concurrent_requests: int = 10
    files_per_request: int = 5
    total_requests: int = 100
    test_file_size_kb: int = 100
    timeout_seconds: int = 300

@dataclass
class TestResult:
    """测试结果"""
    request_id: str
    start_time: float
    end_time: float
    status_code: int
    response_time: float
    success: bool
    error_message: str = ""
    files_processed: int = 0
    files_success: int = 0
    files_failed: int = 0

class PerformanceTestRunner:
    """性能测试运行器"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.results: List[TestResult] = []
        self.test_files: List[bytes] = []
        
    def generate_test_files(self) -> None:
        """生成测试文件"""
        print(f"生成 {self.config.files_per_request} 个测试文件...")
        
        for i in range(self.config.files_per_request):
            # 生成随机PDF内容（模拟）
            content = self._generate_fake_pdf_content(self.config.test_file_size_kb * 1024)
            self.test_files.append(content)
        
        print(f"测试文件生成完成，每个文件大小: {self.config.test_file_size_kb}KB")
    
    def _generate_fake_pdf_content(self, size_bytes: int) -> bytes:
        """生成模拟PDF内容"""
        # PDF文件头
        content = b"%PDF-1.4\n"
        content += b"1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n"
        content += b"2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n"
        content += b"3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] >>\nendobj\n"
        
        # 填充随机内容到指定大小
        remaining_size = size_bytes - len(content) - 20  # 预留结尾空间
        if remaining_size > 0:
            random_content = ''.join(random.choices(string.ascii_letters + string.digits + ' \n', 
                                                  k=remaining_size)).encode()
            content += random_content
        
        # PDF文件尾
        content += b"\nxref\n0 4\ntrailer\n<< /Size 4 /Root 1 0 R >>\nstartxref\n%%EOF"
        
        return content
    
    async def send_batch_request(self, session: aiohttp.ClientSession, request_id: str) -> TestResult:
        """发送批量处理请求"""
        start_time = time.time()
        
        try:
            # 准备multipart数据
            data = aiohttp.FormData()
            
            # 添加文件
            for i, file_content in enumerate(self.test_files):
                data.add_field('files', 
                             file_content,
                             filename=f'test_resume_{request_id}_{i}.pdf',
                             content_type='application/pdf')
            
            # 添加其他参数
            data.add_field('overwriteExisting', 'false')
            data.add_field('parseBasicInfo', 'true')
            data.add_field('parseContactInfo', 'true')
            data.add_field('parseEducation', 'true')
            data.add_field('parseWorkExperience', 'true')
            data.add_field('maxConcurrency', '10')
            data.add_field('timeoutSeconds', str(self.config.timeout_seconds))
            
            # 发送请求
            url = f"{self.config.base_url}/api/admin/batch-resume-upload"
            async with session.post(url, data=data, timeout=self.config.timeout_seconds) as response:
                end_time = time.time()
                response_time = end_time - start_time
                
                response_text = await response.text()
                
                if response.status == 200:
                    try:
                        response_data = json.loads(response_text)
                        if response_data.get('success'):
                            result_data = response_data.get('data', {})
                            statistics = result_data.get('statistics', {})
                            
                            return TestResult(
                                request_id=request_id,
                                start_time=start_time,
                                end_time=end_time,
                                status_code=response.status,
                                response_time=response_time,
                                success=True,
                                files_processed=statistics.get('totalFiles', 0),
                                files_success=statistics.get('successCount', 0),
                                files_failed=statistics.get('failureCount', 0)
                            )
                        else:
                            return TestResult(
                                request_id=request_id,
                                start_time=start_time,
                                end_time=end_time,
                                status_code=response.status,
                                response_time=response_time,
                                success=False,
                                error_message=response_data.get('message', 'Unknown error')
                            )
                    except json.JSONDecodeError:
                        return TestResult(
                            request_id=request_id,
                            start_time=start_time,
                            end_time=end_time,
                            status_code=response.status,
                            response_time=response_time,
                            success=False,
                            error_message="Invalid JSON response"
                        )
                else:
                    return TestResult(
                        request_id=request_id,
                        start_time=start_time,
                        end_time=end_time,
                        status_code=response.status,
                        response_time=response_time,
                        success=False,
                        error_message=f"HTTP {response.status}: {response_text[:200]}"
                    )
                    
        except asyncio.TimeoutError:
            end_time = time.time()
            return TestResult(
                request_id=request_id,
                start_time=start_time,
                end_time=end_time,
                status_code=0,
                response_time=end_time - start_time,
                success=False,
                error_message="Request timeout"
            )
        except Exception as e:
            end_time = time.time()
            return TestResult(
                request_id=request_id,
                start_time=start_time,
                end_time=end_time,
                status_code=0,
                response_time=end_time - start_time,
                success=False,
                error_message=str(e)
            )
    
    async def run_concurrent_test(self) -> None:
        """运行并发测试"""
        print(f"开始并发测试: {self.config.concurrent_requests} 并发, {self.config.total_requests} 总请求")
        
        connector = aiohttp.TCPConnector(limit=self.config.concurrent_requests * 2)
        timeout = aiohttp.ClientTimeout(total=self.config.timeout_seconds)
        
        async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
            semaphore = asyncio.Semaphore(self.config.concurrent_requests)
            
            async def bounded_request(request_id: str) -> TestResult:
                async with semaphore:
                    return await self.send_batch_request(session, request_id)
            
            # 创建所有任务
            tasks = [
                bounded_request(f"req_{i:04d}")
                for i in range(self.config.total_requests)
            ]
            
            # 执行任务并收集结果
            start_time = time.time()
            self.results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # 处理异常结果
            processed_results = []
            for i, result in enumerate(self.results):
                if isinstance(result, Exception):
                    processed_results.append(TestResult(
                        request_id=f"req_{i:04d}",
                        start_time=start_time,
                        end_time=end_time,
                        status_code=0,
                        response_time=0,
                        success=False,
                        error_message=str(result)
                    ))
                else:
                    processed_results.append(result)
            
            self.results = processed_results
            
        print(f"测试完成，总耗时: {end_time - start_time:.2f}秒")
    
    def analyze_results(self) -> Dict[str, Any]:
        """分析测试结果"""
        if not self.results:
            return {}
        
        successful_results = [r for r in self.results if r.success]
        failed_results = [r for r in self.results if not r.success]
        
        response_times = [r.response_time for r in successful_results]
        
        analysis = {
            "总请求数": len(self.results),
            "成功请求数": len(successful_results),
            "失败请求数": len(failed_results),
            "成功率": len(successful_results) / len(self.results) * 100,
            "平均响应时间": sum(response_times) / len(response_times) if response_times else 0,
            "最小响应时间": min(response_times) if response_times else 0,
            "最大响应时间": max(response_times) if response_times else 0,
            "总文件处理数": sum(r.files_processed for r in successful_results),
            "总文件成功数": sum(r.files_success for r in successful_results),
            "总文件失败数": sum(r.files_failed for r in successful_results),
        }
        
        if response_times:
            response_times.sort()
            n = len(response_times)
            analysis["P50响应时间"] = response_times[n // 2]
            analysis["P95响应时间"] = response_times[int(n * 0.95)]
            analysis["P99响应时间"] = response_times[int(n * 0.99)]
        
        # 计算QPS
        if successful_results:
            test_duration = max(r.end_time for r in successful_results) - min(r.start_time for r in successful_results)
            analysis["QPS"] = len(successful_results) / test_duration if test_duration > 0 else 0
        
        return analysis
    
    def print_results(self) -> None:
        """打印测试结果"""
        analysis = self.analyze_results()
        
        print("\n" + "="*60)
        print("性能测试结果")
        print("="*60)
        
        for key, value in analysis.items():
            if isinstance(value, float):
                print(f"{key}: {value:.2f}")
            else:
                print(f"{key}: {value}")
        
        print("\n失败请求详情:")
        failed_results = [r for r in self.results if not r.success]
        for result in failed_results[:10]:  # 只显示前10个失败
            print(f"  {result.request_id}: {result.error_message}")
        
        if len(failed_results) > 10:
            print(f"  ... 还有 {len(failed_results) - 10} 个失败请求")

def main():
    parser = argparse.ArgumentParser(description='批量简历解析性能测试')
    parser.add_argument('--url', default='http://localhost', help='服务器URL')
    parser.add_argument('--concurrent', type=int, default=10, help='并发请求数')
    parser.add_argument('--files', type=int, default=5, help='每个请求的文件数')
    parser.add_argument('--requests', type=int, default=100, help='总请求数')
    parser.add_argument('--file-size', type=int, default=100, help='测试文件大小(KB)')
    parser.add_argument('--timeout', type=int, default=300, help='请求超时时间(秒)')
    
    args = parser.parse_args()
    
    config = TestConfig(
        base_url=args.url,
        concurrent_requests=args.concurrent,
        files_per_request=args.files,
        total_requests=args.requests,
        test_file_size_kb=args.file_size,
        timeout_seconds=args.timeout
    )
    
    runner = PerformanceTestRunner(config)
    runner.generate_test_files()
    
    # 运行测试
    asyncio.run(runner.run_concurrent_test())
    runner.print_results()

if __name__ == "__main__":
    main()
