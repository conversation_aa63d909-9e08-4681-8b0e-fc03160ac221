# 批量简历上传功能说明

## 功能概述

批量简历上传功能允许管理员一次性上传多个简历文件，系统会自动调用第三方解析API进行解析，并根据解析结果自动创建用户账户和完善用户资料信息。

## 主要特性

### 1. 批量处理能力
- 支持同时上传最多20个简历文件
- 可配置的并发处理数量（默认5个）
- 智能的进度跟踪和状态报告

### 2. 文件格式支持
- **PDF文件** (.pdf)
- **Word文档** (.doc, .docx)
- **纯文本** (.txt)

### 3. 用户去重机制
- 基于手机号和姓名进行用户去重
- 支持覆盖现有用户数据的选项
- 智能的重复用户检测和处理

### 4. 完整的数据入库
- **用户基础信息**：姓名、性别、年龄等
- **联系方式**：手机号、邮箱、微信、QQ等
- **教育经历**：学校、学位、专业、时间等
- **工作经历**：公司、职位、部门、薪资等
- **项目经历**：项目名称、角色、描述等
- **技能信息**：IT技能、业务技能等分类

### 5. 错误处理和监控
- 详细的错误分类和处理
- 实时进度跟踪和日志记录
- 批量处理结果统计报告

## API接口

### 批量上传简历

**接口地址：** `POST /api/v1/admin/users/batch-resume-upload`

**请求方式：** `multipart/form-data`

**权限要求：** 管理员权限

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| files | MultipartFile[] | 是 | - | 简历文件数组 |
| overwriteExisting | Boolean | 否 | false | 是否覆盖现有数据 |
| parseBasicInfo | Boolean | 否 | true | 是否解析基本信息 |
| parseContactInfo | Boolean | 否 | true | 是否解析联系方式 |
| parseEducation | Boolean | 否 | true | 是否解析教育经历 |
| parseWorkExperience | Boolean | 否 | true | 是否解析工作经历 |
| parseProjectExperience | Boolean | 否 | true | 是否解析项目经历 |
| parseSkills | Boolean | 否 | true | 是否解析技能信息 |
| parseTraining | Boolean | 否 | true | 是否解析培训经历 |
| parseLanguages | Boolean | 否 | true | 是否解析语言能力 |
| parseCertificates | Boolean | 否 | true | 是否解析证书信息 |
| parseAwards | Boolean | 否 | true | 是否解析获奖记录 |
| maxConcurrency | Integer | 否 | 5 | 最大并发处理数量 |
| timeoutSeconds | Integer | 否 | 60 | 单个文件处理超时时间（秒） |

#### 响应结果

```json
{
  "code": 200,
  "message": "批量简历解析完成",
  "data": {
    "statistics": {
      "totalFiles": 10,
      "successCount": 8,
      "failureCount": 1,
      "skippedCount": 1,
      "newUserCount": 7,
      "updatedUserCount": 1,
      "successRate": 80.0
    },
    "fileResults": [
      {
        "fileName": "resume1.pdf",
        "fileSize": 1024000,
        "status": "SUCCESS",
        "userId": 1001,
        "userOperation": "CREATED",
        "processingTimeMs": 5000,
        "userInfo": {
          "name": "张三",
          "phone": "13800138000",
          "email": "<EMAIL>",
          "gender": "男",
          "age": 30,
          "workExperience": "8年",
          "education": "本科"
        }
      }
    ],
    "startTime": "2024-01-01 10:00:00",
    "endTime": "2024-01-01 10:05:00",
    "totalDurationMs": 300000
  }
}
```

## 配置说明

### application.yml配置

```yaml
# 批量简历上传配置
batch:
  resume:
    upload:
      max-file-count: 20              # 最大文件数量
      max-file-size-mb: 10            # 单个文件最大大小（MB）
      max-total-size-mb: 100          # 批量总大小限制（MB）
      max-concurrency: 5              # 最大并发处理数量
      default-timeout-seconds: 60     # 默认超时时间（秒）
      max-timeout-seconds: 300        # 最大超时时间（秒）
      supported-file-types:           # 支持的文件类型
        - pdf
        - doc
        - docx
        - txt
      enable-file-type-validation: true    # 启用文件类型验证
      enable-file-size-validation: true    # 启用文件大小验证
      enable-virus-scan: false             # 启用病毒扫描
      temp-file-storage-path: /tmp/batch-resume-upload  # 临时文件存储路径
      result-retention-hours: 24           # 处理结果保留时间（小时）
      enable-progress-notification: true   # 启用处理进度通知
      progress-notification-interval: 10   # 进度通知间隔（处理文件数）
      retry:
        enabled: true                      # 启用重试
        max-attempts: 3                    # 最大重试次数
        retry-interval-ms: 1000            # 重试间隔（毫秒）
```

## 使用示例

### cURL示例

```bash
curl -X POST "http://localhost:8080/api/v1/admin/users/batch-resume-upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@resume1.pdf" \
  -F "files=@resume2.pdf" \
  -F "files=@resume3.docx" \
  -F "overwriteExisting=false" \
  -F "parseBasicInfo=true" \
  -F "parseContactInfo=true" \
  -F "parseEducation=true" \
  -F "parseWorkExperience=true" \
  -F "maxConcurrency=3" \
  -F "timeoutSeconds=120"
```

### JavaScript示例

```javascript
const formData = new FormData();
formData.append('files', file1);
formData.append('files', file2);
formData.append('overwriteExisting', 'false');
formData.append('parseBasicInfo', 'true');
formData.append('parseContactInfo', 'true');
formData.append('maxConcurrency', '5');

fetch('/api/v1/admin/users/batch-resume-upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('批量上传结果:', data);
});
```

## 错误处理

### 常见错误类型

1. **FILE_VALIDATION** - 文件验证失败
2. **PARSE_API_ERROR** - 解析API调用失败
3. **DATA_CONVERSION** - 数据转换失败
4. **DATABASE_ERROR** - 数据库操作失败
5. **TIMEOUT** - 处理超时
6. **UNKNOWN** - 未知错误

### 错误响应示例

```json
{
  "code": 400,
  "message": "文件验证失败",
  "data": null
}
```

## 性能建议

1. **文件大小控制**：建议单个文件不超过5MB，以提高处理速度
2. **并发数量**：根据服务器性能调整并发数量，建议不超过10个
3. **批量大小**：建议每批次不超过20个文件，避免长时间等待
4. **网络稳定性**：确保网络连接稳定，避免上传中断

## 监控和日志

系统会记录详细的处理日志，包括：
- 批量处理开始和结束时间
- 每个文件的处理状态和耗时
- 错误信息和异常堆栈
- 用户创建和更新记录

可以通过日志文件或监控系统查看处理状态和性能指标。
