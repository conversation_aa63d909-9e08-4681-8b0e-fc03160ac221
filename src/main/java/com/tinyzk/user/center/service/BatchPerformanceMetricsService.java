package com.tinyzk.user.center.service;

import io.micrometer.core.instrument.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 批量处理性能指标服务
 * 收集和管理批量简历解析的性能指标
 */
@Slf4j
@Service
public class BatchPerformanceMetricsService {

    private final MeterRegistry meterRegistry;

    // 计数器
    private final Counter batchTaskSubmittedCounter;
    private final Counter batchTaskCompletedCounter;
    private final Counter batchTaskFailedCounter;
    private final Counter fileProcessedCounter;
    private final Counter fileSuccessCounter;
    private final Counter fileFailureCounter;
    private final Counter thirdPartyApiCallCounter;
    private final Counter thirdPartyApiErrorCounter;

    // 计时器
    private final Timer batchProcessingTimer;
    private final Timer fileProcessingTimer;
    private final Timer thirdPartyApiTimer;
    private final Timer databaseOperationTimer;

    // 仪表盘
    private final AtomicLong activeBatchTasks = new AtomicLong(0);
    private final AtomicLong activeFileProcessing = new AtomicLong(0);
    private final AtomicLong queuedFiles = new AtomicLong(0);

    // 分布汇总
    private final DistributionSummary batchSizeDistribution;
    private final DistributionSummary processingTimeDistribution;

    @Autowired
    public BatchPerformanceMetricsService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;

        // 初始化计数器
        this.batchTaskSubmittedCounter = Counter.builder("batch.task.submitted.total")
                .description("Total number of batch tasks submitted")
                .register(meterRegistry);

        this.batchTaskCompletedCounter = Counter.builder("batch.task.completed.total")
                .description("Total number of batch tasks completed")
                .register(meterRegistry);

        this.batchTaskFailedCounter = Counter.builder("batch.task.failed.total")
                .description("Total number of batch tasks failed")
                .register(meterRegistry);

        this.fileProcessedCounter = Counter.builder("batch.file.processed.total")
                .description("Total number of files processed")
                .register(meterRegistry);

        this.fileSuccessCounter = Counter.builder("batch.file.success.total")
                .description("Total number of files processed successfully")
                .register(meterRegistry);

        this.fileFailureCounter = Counter.builder("batch.file.failure.total")
                .description("Total number of files failed to process")
                .register(meterRegistry);

        this.thirdPartyApiCallCounter = Counter.builder("third.party.api.call.total")
                .description("Total number of third party API calls")
                .register(meterRegistry);

        this.thirdPartyApiErrorCounter = Counter.builder("third.party.api.error.total")
                .description("Total number of third party API errors")
                .register(meterRegistry);

        // 初始化计时器
        this.batchProcessingTimer = Timer.builder("batch.processing.time")
                .description("Time taken to process a batch")
                .register(meterRegistry);

        this.fileProcessingTimer = Timer.builder("file.processing.time")
                .description("Time taken to process a single file")
                .register(meterRegistry);

        this.thirdPartyApiTimer = Timer.builder("third.party.api.time")
                .description("Time taken for third party API calls")
                .register(meterRegistry);

        this.databaseOperationTimer = Timer.builder("database.operation.time")
                .description("Time taken for database operations")
                .register(meterRegistry);

        // 初始化仪表盘
        Gauge.builder("batch.task.active")
                .description("Number of active batch tasks")
                .register(meterRegistry, activeBatchTasks, AtomicLong::get);

        Gauge.builder("file.processing.active")
                .description("Number of files currently being processed")
                .register(meterRegistry, activeFileProcessing, AtomicLong::get);

        Gauge.builder("file.queued")
                .description("Number of files in queue")
                .register(meterRegistry, queuedFiles, AtomicLong::get);

        // 初始化分布汇总
        this.batchSizeDistribution = DistributionSummary.builder("batch.size")
                .description("Distribution of batch sizes")
                .register(meterRegistry);

        this.processingTimeDistribution = DistributionSummary.builder("processing.time.distribution")
                .description("Distribution of processing times")
                .baseUnit("milliseconds")
                .register(meterRegistry);
    }

    /**
     * 记录批量任务提交
     */
    public void recordBatchTaskSubmitted(int fileCount) {
        batchTaskSubmittedCounter.increment();
        batchSizeDistribution.record(fileCount);
        activeBatchTasks.incrementAndGet();
        queuedFiles.addAndGet(fileCount);
        
        log.debug("记录批量任务提交: fileCount={}", fileCount);
    }

    /**
     * 记录批量任务完成
     */
    public void recordBatchTaskCompleted(Duration processingTime) {
        batchTaskCompletedCounter.increment();
        activeBatchTasks.decrementAndGet();
        processingTimeDistribution.record(processingTime.toMillis());
        
        log.debug("记录批量任务完成: processingTime={}ms", processingTime.toMillis());
    }

    /**
     * 记录批量任务失败
     */
    public void recordBatchTaskFailed(String errorType) {
        batchTaskFailedCounter.increment(Tags.of("error.type", errorType));
        activeBatchTasks.decrementAndGet();
        
        log.debug("记录批量任务失败: errorType={}", errorType);
    }

    /**
     * 记录文件处理开始
     */
    public Timer.Sample startFileProcessing() {
        activeFileProcessing.incrementAndGet();
        queuedFiles.decrementAndGet();
        return Timer.start(meterRegistry);
    }

    /**
     * 记录文件处理完成
     */
    public void recordFileProcessingCompleted(Timer.Sample sample, boolean success, String fileType) {
        sample.stop(fileProcessingTimer.tag("file.type", fileType).tag("success", String.valueOf(success)));
        
        fileProcessedCounter.increment(Tags.of("file.type", fileType));
        
        if (success) {
            fileSuccessCounter.increment(Tags.of("file.type", fileType));
        } else {
            fileFailureCounter.increment(Tags.of("file.type", fileType));
        }
        
        activeFileProcessing.decrementAndGet();
        
        log.debug("记录文件处理完成: success={}, fileType={}", success, fileType);
    }

    /**
     * 记录第三方API调用
     */
    public Timer.Sample startThirdPartyApiCall() {
        return Timer.start(meterRegistry);
    }

    /**
     * 记录第三方API调用完成
     */
    public void recordThirdPartyApiCall(Timer.Sample sample, boolean success, String apiType) {
        sample.stop(thirdPartyApiTimer.tag("api.type", apiType).tag("success", String.valueOf(success)));
        
        thirdPartyApiCallCounter.increment(Tags.of("api.type", apiType));
        
        if (!success) {
            thirdPartyApiErrorCounter.increment(Tags.of("api.type", apiType));
        }
        
        log.debug("记录第三方API调用: success={}, apiType={}", success, apiType);
    }

    /**
     * 记录数据库操作
     */
    public Timer.Sample startDatabaseOperation() {
        return Timer.start(meterRegistry);
    }

    /**
     * 记录数据库操作完成
     */
    public void recordDatabaseOperation(Timer.Sample sample, String operationType) {
        sample.stop(databaseOperationTimer.tag("operation.type", operationType));
        
        log.debug("记录数据库操作: operationType={}", operationType);
    }

    /**
     * 获取当前性能统计
     */
    public PerformanceStats getCurrentStats() {
        return PerformanceStats.builder()
                .activeBatchTasks(activeBatchTasks.get())
                .activeFileProcessing(activeFileProcessing.get())
                .queuedFiles(queuedFiles.get())
                .totalBatchTasksSubmitted((long) batchTaskSubmittedCounter.count())
                .totalBatchTasksCompleted((long) batchTaskCompletedCounter.count())
                .totalBatchTasksFailed((long) batchTaskFailedCounter.count())
                .totalFilesProcessed((long) fileProcessedCounter.count())
                .totalFilesSuccess((long) fileSuccessCounter.count())
                .totalFilesFailure((long) fileFailureCounter.count())
                .totalThirdPartyApiCalls((long) thirdPartyApiCallCounter.count())
                .totalThirdPartyApiErrors((long) thirdPartyApiErrorCounter.count())
                .avgBatchProcessingTime(batchProcessingTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS))
                .avgFileProcessingTime(fileProcessingTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS))
                .avgThirdPartyApiTime(thirdPartyApiTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS))
                .build();
    }

    /**
     * 性能统计数据类
     */
    @lombok.Data
    @lombok.Builder
    public static class PerformanceStats {
        private long activeBatchTasks;
        private long activeFileProcessing;
        private long queuedFiles;
        private long totalBatchTasksSubmitted;
        private long totalBatchTasksCompleted;
        private long totalBatchTasksFailed;
        private long totalFilesProcessed;
        private long totalFilesSuccess;
        private long totalFilesFailure;
        private long totalThirdPartyApiCalls;
        private long totalThirdPartyApiErrors;
        private double avgBatchProcessingTime;
        private double avgFileProcessingTime;
        private double avgThirdPartyApiTime;
    }
}
