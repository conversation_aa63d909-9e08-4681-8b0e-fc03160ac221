package com.tinyzk.user.center.service;

import com.rabbitmq.client.Channel;
import com.tinyzk.user.center.config.RabbitMQConfig;
import com.tinyzk.user.center.dto.ResumeParseTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 简历解析消息消费者
 * 负责处理队列中的批量解析任务
 */
@Slf4j
@Service
public class ResumeParseMessageConsumer {

    @Autowired
    private BatchResumeParseServiceImpl batchResumeParseService;

    @Autowired
    private BatchProgressNotificationService progressNotificationService;

    @Autowired
    private BatchPerformanceMetricsService metricsService;

    /**
     * 处理普通批量解析任务
     * 
     * @param taskMessage 任务消息
     * @param message 原始消息
     * @param channel 通道
     * @param deliveryTag 投递标签
     */
    @RabbitListener(queues = RabbitMQConfig.RESUME_PARSE_QUEUE, concurrency = "5-20")
    public void processBatchParseTask(
            @Payload ResumeParseTaskMessage taskMessage,
            Message message,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {

        String taskId = taskMessage.getTaskId();
        String subTaskId = taskMessage.getSubTaskId();
        
        log.info("开始处理批量解析任务: taskId={}, subTaskId={}, fileCount={}", 
                taskId, subTaskId, taskMessage.getFileUrls().size());

        // 记录开始处理指标
        var processingTimer = metricsService.startFileProcessing();
        
        try {
            // 更新任务状态为处理中
            progressNotificationService.setTaskStatus(subTaskId, "PROCESSING", null);

            // 处理子任务
            processSubTask(taskMessage);

            // 手动确认消息
            channel.basicAck(deliveryTag, false);
            
            // 更新任务状态为完成
            progressNotificationService.setTaskStatus(subTaskId, "COMPLETED", null);
            
            // 记录处理完成指标
            metricsService.recordFileProcessingCompleted(processingTimer, true, "batch");
            
            log.info("批量解析任务处理完成: taskId={}, subTaskId={}", taskId, subTaskId);

        } catch (Exception e) {
            log.error("处理批量解析任务失败: taskId={}, subTaskId={}", taskId, subTaskId, e);
            
            try {
                // 检查重试次数
                Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get("retryCount");
                if (retryCount == null) retryCount = 0;

                if (retryCount < 3) {
                    // 重试：拒绝消息并重新入队
                    log.info("任务处理失败，准备重试: taskId={}, subTaskId={}, retryCount={}", 
                            taskId, subTaskId, retryCount + 1);
                    
                    // 更新重试次数
                    message.getMessageProperties().getHeaders().put("retryCount", retryCount + 1);
                    
                    // 拒绝消息，重新入队
                    channel.basicNack(deliveryTag, false, true);
                } else {
                    // 超过重试次数，发送到死信队列
                    log.error("任务处理失败，超过最大重试次数，发送到死信队列: taskId={}, subTaskId={}", 
                            taskId, subTaskId);
                    
                    // 拒绝消息，不重新入队（会进入死信队列）
                    channel.basicNack(deliveryTag, false, false);
                    
                    // 更新任务状态为失败
                    progressNotificationService.setTaskStatus(subTaskId, "FAILED", e.getMessage());
                }
                
                // 记录处理失败指标
                metricsService.recordFileProcessingCompleted(processingTimer, false, "batch");
                
            } catch (IOException ioException) {
                log.error("消息确认失败: taskId={}, subTaskId={}", taskId, subTaskId, ioException);
            }
        }
    }

    /**
     * 处理高优先级任务
     */
    @RabbitListener(queues = RabbitMQConfig.RESUME_PARSE_PRIORITY_QUEUE, concurrency = "3-10")
    public void processPriorityParseTask(
            @Payload ResumeParseTaskMessage taskMessage,
            Message message,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {

        String taskId = taskMessage.getTaskId();
        String subTaskId = taskMessage.getSubTaskId();
        Integer priority = (Integer) message.getMessageProperties().getHeaders().get("priority");
        
        log.info("开始处理高优先级解析任务: taskId={}, subTaskId={}, priority={}, fileCount={}", 
                taskId, subTaskId, priority, taskMessage.getFileUrls().size());

        var processingTimer = metricsService.startFileProcessing();
        
        try {
            // 高优先级任务处理逻辑
            progressNotificationService.setTaskStatus(subTaskId, "PROCESSING", null);
            
            // 异步处理，但设置更短的超时时间
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                processSubTask(taskMessage);
            });
            
            // 高优先级任务超时时间更短
            future.get(15, TimeUnit.MINUTES);

            channel.basicAck(deliveryTag, false);
            progressNotificationService.setTaskStatus(subTaskId, "COMPLETED", null);
            metricsService.recordFileProcessingCompleted(processingTimer, true, "priority");
            
            log.info("高优先级任务处理完成: taskId={}, subTaskId={}, priority={}", 
                    taskId, subTaskId, priority);

        } catch (Exception e) {
            log.error("处理高优先级任务失败: taskId={}, subTaskId={}, priority={}", 
                    taskId, subTaskId, priority, e);
            
            try {
                // 高优先级任务失败直接进入死信队列，不重试
                channel.basicNack(deliveryTag, false, false);
                progressNotificationService.setTaskStatus(subTaskId, "FAILED", e.getMessage());
                metricsService.recordFileProcessingCompleted(processingTimer, false, "priority");
                
            } catch (IOException ioException) {
                log.error("高优先级任务消息确认失败: taskId={}, subTaskId={}", taskId, subTaskId, ioException);
            }
        }
    }

    /**
     * 处理死信队列中的消息
     */
    @RabbitListener(queues = RabbitMQConfig.RESUME_PARSE_DLQ, concurrency = "1-3")
    public void processDeadLetterTask(
            @Payload ResumeParseTaskMessage taskMessage,
            Message message,
            Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {

        String taskId = taskMessage.getTaskId();
        String subTaskId = taskMessage.getSubTaskId();
        
        log.warn("处理死信队列任务: taskId={}, subTaskId={}, fileCount={}", 
                taskId, subTaskId, taskMessage.getFileUrls().size());

        try {
            // 记录死信任务信息
            recordDeadLetterTask(taskMessage, message);
            
            // 可以选择：
            // 1. 人工干预处理
            // 2. 发送告警通知
            // 3. 记录到数据库等待后续处理
            // 4. 尝试降级处理
            
            // 这里选择记录并通知管理员
            notifyAdminForFailedTask(taskMessage, message);
            
            // 确认消息（从死信队列中移除）
            channel.basicAck(deliveryTag, false);
            
            log.info("死信任务处理完成: taskId={}, subTaskId={}", taskId, subTaskId);

        } catch (Exception e) {
            log.error("处理死信任务失败: taskId={}, subTaskId={}", taskId, subTaskId, e);
            
            try {
                // 死信队列处理失败，直接确认（避免无限循环）
                channel.basicAck(deliveryTag, false);
            } catch (IOException ioException) {
                log.error("死信任务消息确认失败: taskId={}, subTaskId={}", taskId, subTaskId, ioException);
            }
        }
    }

    /**
     * 处理子任务的核心逻辑
     */
    private void processSubTask(ResumeParseTaskMessage taskMessage) {
        try {
            log.info("开始处理子任务: subTaskId={}, fileCount={}", 
                    taskMessage.getSubTaskId(), taskMessage.getFileUrls().size());

            // 这里调用实际的批量处理逻辑
            // 注意：需要将OSS文件URL转换为MultipartFile或InputStream
            
            // 1. 从OSS下载文件
            // 2. 调用第三方解析API
            // 3. 保存解析结果到数据库
            // 4. 更新进度
            
            // 模拟处理逻辑
            for (String fileUrl : taskMessage.getFileUrls()) {
                try {
                    // 处理单个文件
                    processFile(fileUrl, taskMessage);
                    
                    // 更新进度
                    progressNotificationService.updateFileProgress(
                            taskMessage.getSubTaskId(), 
                            extractFileName(fileUrl), 
                            true, 
                            false, 
                            System.currentTimeMillis()
                    );
                    
                } catch (Exception e) {
                    log.error("处理文件失败: fileUrl={}, subTaskId={}", fileUrl, taskMessage.getSubTaskId(), e);
                    
                    // 更新进度（失败）
                    progressNotificationService.updateFileProgress(
                            taskMessage.getSubTaskId(), 
                            extractFileName(fileUrl), 
                            false, 
                            false, 
                            System.currentTimeMillis()
                    );
                }
            }

            log.info("子任务处理完成: subTaskId={}", taskMessage.getSubTaskId());

        } catch (Exception e) {
            log.error("子任务处理失败: subTaskId={}", taskMessage.getSubTaskId(), e);
            throw new RuntimeException("子任务处理失败", e);
        }
    }

    /**
     * 处理单个文件
     */
    private void processFile(String fileUrl, ResumeParseTaskMessage taskMessage) {
        // 实际的文件处理逻辑
        // 1. 从OSS下载文件
        // 2. 调用第三方解析API
        // 3. 保存结果到数据库
        
        log.debug("处理文件: fileUrl={}, subTaskId={}", fileUrl, taskMessage.getSubTaskId());
        
        // 模拟处理时间
        try {
            Thread.sleep(1000); // 模拟1秒处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 记录死信任务
     */
    private void recordDeadLetterTask(ResumeParseTaskMessage taskMessage, Message message) {
        log.warn("记录死信任务: taskId={}, subTaskId={}, 原因: 超过最大重试次数", 
                taskMessage.getTaskId(), taskMessage.getSubTaskId());
        
        // 可以保存到数据库或发送到监控系统
    }

    /**
     * 通知管理员处理失败任务
     */
    private void notifyAdminForFailedTask(ResumeParseTaskMessage taskMessage, Message message) {
        log.error("通知管理员处理失败任务: taskId={}, subTaskId={}", 
                taskMessage.getTaskId(), taskMessage.getSubTaskId());
        
        // 可以发送邮件、短信或推送到监控系统
    }

    /**
     * 从文件URL提取文件名
     */
    private String extractFileName(String fileUrl) {
        if (fileUrl == null || fileUrl.isEmpty()) {
            return "unknown";
        }
        
        int lastSlash = fileUrl.lastIndexOf('/');
        return lastSlash >= 0 ? fileUrl.substring(lastSlash + 1) : fileUrl;
    }
}
