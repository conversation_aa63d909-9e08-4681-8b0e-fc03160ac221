package com.tinyzk.user.center.service;

import com.tinyzk.user.center.config.RabbitMQConfig;
import com.tinyzk.user.center.dto.ResumeParseTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 消息队列管理服务
 * 提供队列监控、管理和统计功能
 */
@Slf4j
@Service
public class MessageQueueManagementService {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitAdmin rabbitAdmin;

    @Autowired
    private ResumeParseMessageProducer messageProducer;

    /**
     * 将大任务分解为子任务并发送到队列
     *
     * @param mainTaskId 主任务ID
     * @param allFileUrls 所有文件URL
     * @param parseOptions 解析选项
     * @param subTaskSize 子任务大小
     */
    public void splitAndSendTasks(String mainTaskId, List<String> allFileUrls, 
                                 ResumeParseTaskMessage.ParseOptions parseOptions, 
                                 int subTaskSize) {
        
        log.info("开始分解大任务: mainTaskId={}, totalFiles={}, subTaskSize={}", 
                mainTaskId, allFileUrls.size(), subTaskSize);

        try {
            // 计算子任务数量
            int totalSubTasks = (int) Math.ceil((double) allFileUrls.size() / subTaskSize);
            log.info("将创建 {} 个子任务", totalSubTasks);

            // 分解并发送子任务
            for (int i = 0; i < totalSubTasks; i++) {
                int startIndex = i * subTaskSize;
                int endIndex = Math.min(startIndex + subTaskSize, allFileUrls.size());
                
                List<String> subTaskFiles = allFileUrls.subList(startIndex, endIndex);
                String subTaskId = mainTaskId + "_sub_" + (i + 1);

                // 创建子任务消息
                ResumeParseTaskMessage subTaskMessage = ResumeParseTaskMessage.createNormalTask(
                        mainTaskId, subTaskId, subTaskFiles, parseOptions);

                // 根据文件数量决定优先级
                if (subTaskFiles.size() <= 5) {
                    // 小批量任务使用高优先级
                    messageProducer.sendPriorityParseTask(subTaskMessage, 8);
                } else {
                    // 大批量任务使用普通优先级
                    messageProducer.sendBatchParseTask(subTaskMessage);
                }

                log.info("子任务已发送: subTaskId={}, fileCount={}", subTaskId, subTaskFiles.size());
            }

            log.info("大任务分解完成: mainTaskId={}, 共创建 {} 个子任务", mainTaskId, totalSubTasks);

        } catch (Exception e) {
            log.error("分解大任务失败: mainTaskId={}", mainTaskId, e);
            throw new RuntimeException("任务分解失败", e);
        }
    }

    /**
     * 获取队列统计信息
     */
    public QueueStatistics getQueueStatistics() {
        try {
            QueueStatistics stats = new QueueStatistics();

            // 获取普通队列信息
            QueueInformation normalQueueInfo = rabbitAdmin.getQueueInfo(RabbitMQConfig.RESUME_PARSE_QUEUE);
            if (normalQueueInfo != null) {
                stats.setNormalQueueMessageCount(normalQueueInfo.getMessageCount());
                stats.setNormalQueueConsumerCount(normalQueueInfo.getConsumerCount());
            }

            // 获取优先级队列信息
            QueueInformation priorityQueueInfo = rabbitAdmin.getQueueInfo(RabbitMQConfig.RESUME_PARSE_PRIORITY_QUEUE);
            if (priorityQueueInfo != null) {
                stats.setPriorityQueueMessageCount(priorityQueueInfo.getMessageCount());
                stats.setPriorityQueueConsumerCount(priorityQueueInfo.getConsumerCount());
            }

            // 获取死信队列信息
            QueueInformation dlqInfo = rabbitAdmin.getQueueInfo(RabbitMQConfig.RESUME_PARSE_DLQ);
            if (dlqInfo != null) {
                stats.setDeadLetterQueueMessageCount(dlqInfo.getMessageCount());
            }

            log.debug("队列统计信息: {}", stats);
            return stats;

        } catch (Exception e) {
            log.error("获取队列统计信息失败", e);
            return new QueueStatistics(); // 返回空统计信息
        }
    }

    /**
     * 清空指定队列
     */
    public boolean purgeQueue(String queueName) {
        try {
            rabbitAdmin.purgeQueue(queueName);
            log.info("队列清空成功: queueName={}", queueName);
            return true;
        } catch (Exception e) {
            log.error("清空队列失败: queueName={}", queueName, e);
            return false;
        }
    }

    /**
     * 发送测试消息
     */
    public void sendTestMessage(int messageCount) {
        log.info("开始发送测试消息: messageCount={}", messageCount);

        for (int i = 1; i <= messageCount; i++) {
            try {
                String testTaskId = "test_" + UUID.randomUUID().toString().substring(0, 8);
                String subTaskId = testTaskId + "_sub_1";

                ResumeParseTaskMessage testMessage = ResumeParseTaskMessage.builder()
                        .taskId(testTaskId)
                        .subTaskId(subTaskId)
                        .fileUrls(List.of("test://file" + i + ".pdf"))
                        .parseOptions(ResumeParseTaskMessage.ParseOptions.builder()
                                .parseBasicInfo(true)
                                .parseContactInfo(true)
                                .maxConcurrency(5)
                                .build())
                        .priority(5)
                        .highPriority(false)
                        .timeoutSeconds(60)
                        .maxRetries(3)
                        .build();

                messageProducer.sendBatchParseTask(testMessage);
                log.debug("测试消息发送成功: {}/{}", i, messageCount);

            } catch (Exception e) {
                log.error("发送测试消息失败: {}/{}", i, messageCount, e);
            }
        }

        log.info("测试消息发送完成: messageCount={}", messageCount);
    }

    /**
     * 重新处理死信队列中的消息
     */
    public void reprocessDeadLetterMessages(int maxCount) {
        log.info("开始重新处理死信队列消息: maxCount={}", maxCount);

        try {
            // 这里需要实现从死信队列读取消息并重新发送到正常队列的逻辑
            // 由于RabbitMQ的限制，这通常需要通过Management API或自定义逻辑实现
            
            log.warn("死信队列重处理功能需要通过Management API实现");
            
        } catch (Exception e) {
            log.error("重新处理死信队列消息失败", e);
        }
    }

    /**
     * 动态调整消费者数量
     */
    public void adjustConsumerCount(String queueName, int targetConsumerCount) {
        log.info("动态调整消费者数量: queueName={}, targetCount={}", queueName, targetConsumerCount);
        
        // 这个功能需要通过RabbitMQ Management API或自定义监听器容器管理实现
        // 这里只是记录日志
        log.warn("动态调整消费者数量功能需要额外实现");
    }

    /**
     * 获取队列健康状态
     */
    public QueueHealthStatus getQueueHealthStatus() {
        try {
            QueueStatistics stats = getQueueStatistics();
            QueueHealthStatus status = new QueueHealthStatus();

            // 检查队列消息堆积情况
            if (stats.getNormalQueueMessageCount() > 1000) {
                status.setNormalQueueStatus("WARNING");
                status.addIssue("普通队列消息堆积: " + stats.getNormalQueueMessageCount());
            } else {
                status.setNormalQueueStatus("HEALTHY");
            }

            if (stats.getPriorityQueueMessageCount() > 500) {
                status.setPriorityQueueStatus("WARNING");
                status.addIssue("优先级队列消息堆积: " + stats.getPriorityQueueMessageCount());
            } else {
                status.setPriorityQueueStatus("HEALTHY");
            }

            if (stats.getDeadLetterQueueMessageCount() > 100) {
                status.setDeadLetterQueueStatus("ERROR");
                status.addIssue("死信队列消息过多: " + stats.getDeadLetterQueueMessageCount());
            } else {
                status.setDeadLetterQueueStatus("HEALTHY");
            }

            // 检查消费者数量
            if (stats.getNormalQueueConsumerCount() == 0) {
                status.addIssue("普通队列没有消费者");
            }

            if (stats.getPriorityQueueConsumerCount() == 0) {
                status.addIssue("优先级队列没有消费者");
            }

            return status;

        } catch (Exception e) {
            log.error("获取队列健康状态失败", e);
            QueueHealthStatus errorStatus = new QueueHealthStatus();
            errorStatus.addIssue("无法获取队列状态: " + e.getMessage());
            return errorStatus;
        }
    }

    /**
     * 队列统计信息
     */
    @lombok.Data
    public static class QueueStatistics {
        private int normalQueueMessageCount = 0;
        private int normalQueueConsumerCount = 0;
        private int priorityQueueMessageCount = 0;
        private int priorityQueueConsumerCount = 0;
        private int deadLetterQueueMessageCount = 0;
    }

    /**
     * 队列健康状态
     */
    @lombok.Data
    public static class QueueHealthStatus {
        private String normalQueueStatus = "UNKNOWN";
        private String priorityQueueStatus = "UNKNOWN";
        private String deadLetterQueueStatus = "UNKNOWN";
        private java.util.List<String> issues = new java.util.ArrayList<>();

        public void addIssue(String issue) {
            this.issues.add(issue);
        }

        public boolean isHealthy() {
            return issues.isEmpty() && 
                   "HEALTHY".equals(normalQueueStatus) && 
                   "HEALTHY".equals(priorityQueueStatus) && 
                   "HEALTHY".equals(deadLetterQueueStatus);
        }
    }
}
