package com.tinyzk.user.center.service;

import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.vo.AsyncBatchTaskVO;

/**
 * 异步批量简历解析服务接口
 */
public interface AsyncBatchResumeParseService {

    /**
     * 提交异步批量解析任务
     *
     * @param requestDTO 批量解析请求
     * @return 异步任务信息
     */
    AsyncBatchTaskVO submitAsyncBatch(BatchResumeUploadRequestDTO requestDTO);

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    AsyncBatchTaskVO getTaskStatus(String taskId);
}
