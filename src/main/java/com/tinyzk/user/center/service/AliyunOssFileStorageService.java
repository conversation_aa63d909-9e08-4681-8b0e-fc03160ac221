package com.tinyzk.user.center.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.tinyzk.user.center.config.AliyunOssConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 阿里云OSS加密文件存储服务
 * 支持服务端加密和客户端加密
 */
@Slf4j
@Service
public class AliyunOssFileStorageService implements FileStorageService {

    @Autowired
    private OSS ossClient;

    @Autowired
    private AliyunOssConfig ossConfig;

    @Override
    public String uploadFile(MultipartFile file, String taskId) throws IOException {
        String fileName = generateFileName(file.getOriginalFilename(), taskId);
        String objectKey = ossConfig.getFullPath(fileName);

        try {
            // 根据配置选择加密方式
            if (ossConfig.getEncryption().getClientSideEnabled()) {
                return uploadWithClientSideEncryption(file, objectKey);
            } else {
                return uploadWithServerSideEncryption(file, objectKey);
            }
        } catch (Exception e) {
            log.error("文件上传失败: fileName={}, taskId={}", fileName, taskId, e);
            throw new IOException("文件上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 服务端加密上传
     */
    private String uploadWithServerSideEncryption(MultipartFile file, String objectKey) throws IOException {
        log.info("开始服务端加密上传: objectKey={}", objectKey);

        // 创建上传请求
        PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(),
                objectKey,
                file.getInputStream()
        );

        // 设置对象元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.getSize());
        metadata.setContentType(file.getContentType());
        metadata.setContentDisposition("attachment; filename=\"" + file.getOriginalFilename() + "\"");

        // 添加自定义元数据
        metadata.addUserMetadata("original-name", file.getOriginalFilename());
        metadata.addUserMetadata("upload-time", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        metadata.addUserMetadata("file-size", String.valueOf(file.getSize()));

        // 配置服务端加密
        if (ossConfig.getEncryption().getEnabled()) {
            String algorithm = ossConfig.getEncryption().getAlgorithm();
            
            switch (algorithm.toUpperCase()) {
                case "AES256":
                    metadata.setServerSideEncryption(ObjectMetadata.AES_256_SERVER_SIDE_ENCRYPTION);
                    log.info("使用AES256服务端加密");
                    break;
                    
                case "KMS":
                    metadata.setServerSideEncryption(ObjectMetadata.KMS_SERVER_SIDE_ENCRYPTION);
                    if (ossConfig.getEncryption().getKmsKeyId() != null) {
                        metadata.setSSEKMSKeyId(ossConfig.getEncryption().getKmsKeyId());
                    }
                    if (ossConfig.getEncryption().getKmsEncryptionContext() != null) {
                        metadata.setSSEKMSEncryptionContext(ossConfig.getEncryption().getKmsEncryptionContext());
                    }
                    log.info("使用KMS服务端加密: keyId={}", ossConfig.getEncryption().getKmsKeyId());
                    break;
                    
                case "SM4":
                    metadata.setServerSideEncryption("SM4");
                    log.info("使用SM4服务端加密");
                    break;
                    
                default:
                    log.warn("不支持的加密算法: {}, 使用默认AES256", algorithm);
                    metadata.setServerSideEncryption(ObjectMetadata.AES_256_SERVER_SIDE_ENCRYPTION);
            }
        }

        putObjectRequest.setMetadata(metadata);

        // 执行上传
        PutObjectResult result = ossClient.putObject(putObjectRequest);
        
        log.info("服务端加密上传完成: objectKey={}, etag={}", objectKey, result.getETag());
        return objectKey;
    }

    /**
     * 客户端加密上传
     */
    private String uploadWithClientSideEncryption(MultipartFile file, String objectKey) throws Exception {
        log.info("开始客户端加密上传: objectKey={}", objectKey);

        // 生成加密密钥和IV
        SecretKey secretKey = generateSecretKey();
        byte[] iv = generateIV();

        // 加密文件内容
        byte[] encryptedData = encryptData(file.getBytes(), secretKey, iv);

        // 创建上传请求
        PutObjectRequest putObjectRequest = new PutObjectRequest(
                ossConfig.getBucketName(),
                objectKey,
                new ByteArrayInputStream(encryptedData)
        );

        // 设置对象元数据
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(encryptedData.length);
        metadata.setContentType("application/octet-stream"); // 加密后的数据

        // 保存加密信息到元数据
        metadata.addUserMetadata("original-name", file.getOriginalFilename());
        metadata.addUserMetadata("original-content-type", file.getContentType());
        metadata.addUserMetadata("original-size", String.valueOf(file.getSize()));
        metadata.addUserMetadata("upload-time", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        metadata.addUserMetadata("encryption-algorithm", ossConfig.getEncryption().getClientSideAlgorithm());
        metadata.addUserMetadata("encryption-key", Base64.getEncoder().encodeToString(secretKey.getEncoded()));
        metadata.addUserMetadata("encryption-iv", Base64.getEncoder().encodeToString(iv));
        metadata.addUserMetadata("client-side-encrypted", "true");

        putObjectRequest.setMetadata(metadata);

        // 执行上传
        PutObjectResult result = ossClient.putObject(putObjectRequest);
        
        log.info("客户端加密上传完成: objectKey={}, etag={}", objectKey, result.getETag());
        return objectKey;
    }

    @Override
    public InputStream downloadFile(String objectKey) throws IOException {
        try {
            // 获取对象元数据
            ObjectMetadata metadata = ossClient.getObjectMetadata(ossConfig.getBucketName(), objectKey);
            
            // 检查是否为客户端加密文件
            if ("true".equals(metadata.getUserMetadata().get("client-side-encrypted"))) {
                return downloadWithClientSideDecryption(objectKey, metadata);
            } else {
                return downloadWithServerSideDecryption(objectKey);
            }
        } catch (Exception e) {
            log.error("文件下载失败: objectKey={}", objectKey, e);
            throw new IOException("文件下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 服务端解密下载
     */
    private InputStream downloadWithServerSideDecryption(String objectKey) {
        log.info("开始服务端解密下载: objectKey={}", objectKey);
        
        OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), objectKey);
        return ossObject.getObjectContent();
    }

    /**
     * 客户端解密下载
     */
    private InputStream downloadWithClientSideDecryption(String objectKey, ObjectMetadata metadata) throws Exception {
        log.info("开始客户端解密下载: objectKey={}", objectKey);

        // 获取加密信息
        String encryptionKey = metadata.getUserMetadata().get("encryption-key");
        String encryptionIv = metadata.getUserMetadata().get("encryption-iv");
        String algorithm = metadata.getUserMetadata().get("encryption-algorithm");

        if (encryptionKey == null || encryptionIv == null) {
            throw new IllegalStateException("缺少解密信息");
        }

        // 下载加密数据
        OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), objectKey);
        byte[] encryptedData = ossObject.getObjectContent().readAllBytes();

        // 解密数据
        SecretKey secretKey = new SecretKeySpec(Base64.getDecoder().decode(encryptionKey), "AES");
        byte[] iv = Base64.getDecoder().decode(encryptionIv);
        byte[] decryptedData = decryptData(encryptedData, secretKey, iv);

        return new ByteArrayInputStream(decryptedData);
    }

    @Override
    public boolean deleteFile(String objectKey) {
        try {
            ossClient.deleteObject(ossConfig.getBucketName(), objectKey);
            log.info("文件删除成功: objectKey={}", objectKey);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败: objectKey={}", objectKey, e);
            return false;
        }
    }

    @Override
    public String getFileUrl(String objectKey) {
        return ossConfig.getFileUrl(objectKey.replace(ossConfig.getBasePath(), ""));
    }

    @Override
    public boolean fileExists(String objectKey) {
        try {
            return ossClient.doesObjectExist(ossConfig.getBucketName(), objectKey);
        } catch (Exception e) {
            log.error("检查文件存在性失败: objectKey={}", objectKey, e);
            return false;
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String originalFilename, String taskId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        String extension = getFileExtension(originalFilename);
        
        return String.format("%s_%s_%s%s", taskId, timestamp, uuid, extension);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    /**
     * 生成AES密钥
     */
    private SecretKey generateSecretKey() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(256);
        return keyGenerator.generateKey();
    }

    /**
     * 生成初始化向量
     */
    private byte[] generateIV() {
        byte[] iv = new byte[16];
        new SecureRandom().nextBytes(iv);
        return iv;
    }

    /**
     * 加密数据
     */
    private byte[] encryptData(byte[] data, SecretKey key, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance(ossConfig.getEncryption().getClientSideAlgorithm());
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec);
        return cipher.doFinal(data);
    }

    /**
     * 解密数据
     */
    private byte[] decryptData(byte[] encryptedData, SecretKey key, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance(ossConfig.getEncryption().getClientSideAlgorithm());
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, key, ivSpec);
        return cipher.doFinal(encryptedData);
    }
}
