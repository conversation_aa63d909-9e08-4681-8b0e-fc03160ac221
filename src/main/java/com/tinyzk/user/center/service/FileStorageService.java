package com.tinyzk.user.center.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * 文件存储服务接口
 * 支持多种存储后端实现
 */
public interface FileStorageService {

    /**
     * 上传文件
     *
     * @param file   要上传的文件
     * @param taskId 任务ID，用于文件分组
     * @return 文件存储路径/键值
     * @throws IOException 上传失败时抛出
     */
    String uploadFile(MultipartFile file, String taskId) throws IOException;

    /**
     * 下载文件
     *
     * @param objectKey 文件存储路径/键值
     * @return 文件输入流
     * @throws IOException 下载失败时抛出
     */
    InputStream downloadFile(String objectKey) throws IOException;

    /**
     * 删除文件
     *
     * @param objectKey 文件存储路径/键值
     * @return 删除是否成功
     */
    boolean deleteFile(String objectKey);

    /**
     * 获取文件访问URL
     *
     * @param objectKey 文件存储路径/键值
     * @return 文件访问URL
     */
    String getFileUrl(String objectKey);

    /**
     * 检查文件是否存在
     *
     * @param objectKey 文件存储路径/键值
     * @return 文件是否存在
     */
    boolean fileExists(String objectKey);
}
