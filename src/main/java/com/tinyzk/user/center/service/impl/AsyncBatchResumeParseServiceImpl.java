package com.tinyzk.user.center.service.impl;

import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.vo.AsyncBatchTaskVO;
import com.tinyzk.user.center.service.AsyncBatchResumeParseService;
import com.tinyzk.user.center.service.BatchProgressNotificationService;
import com.tinyzk.user.center.service.AliyunOssFileStorageService;
import com.tinyzk.user.center.common.util.AsyncBatchProgressTracker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * 异步批量简历解析服务实现
 * 支持大规模批量处理和实时进度反馈
 */
@Slf4j
@Service
public class AsyncBatchResumeParseServiceImpl implements AsyncBatchResumeParseService {

    @Autowired
    @Qualifier("batchResumeParseExecutor")
    private Executor batchResumeParseExecutor;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private BatchProgressNotificationService progressNotificationService;

    @Autowired
    private AliyunOssFileStorageService fileStorageService;

    @Autowired
    private BatchResumeParseServiceImpl batchResumeParseService;

    @Override
    public AsyncBatchTaskVO submitAsyncBatch(BatchResumeUploadRequestDTO requestDTO) {
        String taskId = generateTaskId();
        log.info("提交异步批量解析任务: taskId={}, fileCount={}", taskId, requestDTO.getFiles().length);

        try {
            // 1. 创建任务记录
            AsyncBatchTaskVO taskVO = createAsyncTask(taskId, requestDTO);
            
            // 2. 保存任务到Redis
            saveTaskToRedis(taskId, taskVO);
            
            // 3. 异步处理文件
            CompletableFuture.runAsync(() -> processAsyncBatch(taskId, requestDTO), batchResumeParseExecutor)
                    .exceptionally(throwable -> {
                        log.error("异步批量处理失败: taskId={}", taskId, throwable);
                        updateTaskStatus(taskId, "FAILED", throwable.getMessage());
                        return null;
                    });

            return taskVO;
            
        } catch (Exception e) {
            log.error("提交异步批量任务失败: taskId={}", taskId, e);
            throw new RuntimeException("提交异步批量任务失败: " + e.getMessage());
        }
    }

    @Override
    public AsyncBatchTaskVO getTaskStatus(String taskId) {
        try {
            AsyncBatchTaskVO task = (AsyncBatchTaskVO) redisTemplate.opsForValue()
                    .get("async:batch:task:" + taskId);
            
            if (task == null) {
                throw new RuntimeException("任务不存在: " + taskId);
            }
            
            return task;
        } catch (Exception e) {
            log.error("获取任务状态失败: taskId={}", taskId, e);
            throw new RuntimeException("获取任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 异步处理批量文件
     */
    private void processAsyncBatch(String taskId, BatchResumeUploadRequestDTO requestDTO) {
        log.info("开始异步处理批量文件: taskId={}", taskId);
        
        try {
            // 1. 更新任务状态为处理中
            updateTaskStatus(taskId, "PROCESSING", null);
            
            // 2. 上传文件到对象存储
            uploadFilesToStorage(taskId, requestDTO.getFiles());
            
            // 3. 创建进度跟踪器
            AsyncBatchProgressTracker progressTracker = new AsyncBatchProgressTracker(
                    taskId, requestDTO.getFiles().length, progressNotificationService);
            
            // 4. 分批处理文件
            processBatchInChunks(taskId, requestDTO, progressTracker);
            
            // 5. 更新任务状态为完成
            updateTaskStatus(taskId, "COMPLETED", null);
            
            log.info("异步批量处理完成: taskId={}", taskId);
            
        } catch (Exception e) {
            log.error("异步批量处理失败: taskId={}", taskId, e);
            updateTaskStatus(taskId, "FAILED", e.getMessage());
        }
    }

    /**
     * 分批处理文件
     */
    private void processBatchInChunks(String taskId, BatchResumeUploadRequestDTO requestDTO, 
                                    AsyncBatchProgressTracker progressTracker) {
        
        MultipartFile[] files = requestDTO.getFiles();
        int chunkSize = Math.min(20, files.length); // 每批最多20个文件
        
        for (int i = 0; i < files.length; i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, files.length);
            MultipartFile[] chunk = new MultipartFile[endIndex - i];
            System.arraycopy(files, i, chunk, 0, endIndex - i);
            
            // 处理当前批次
            processFileChunk(taskId, chunk, requestDTO, progressTracker);
            
            // 批次间隔，避免系统过载
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("处理被中断", e);
            }
        }
    }

    /**
     * 处理文件块
     */
    private void processFileChunk(String taskId, MultipartFile[] chunk, 
                                BatchResumeUploadRequestDTO requestDTO,
                                AsyncBatchProgressTracker progressTracker) {
        
        // 创建临时请求DTO
        BatchResumeUploadRequestDTO chunkRequest = new BatchResumeUploadRequestDTO();
        chunkRequest.setFiles(chunk);
        chunkRequest.setOverwriteExisting(requestDTO.getOverwriteExisting());
        chunkRequest.setParseBasicInfo(requestDTO.getParseBasicInfo());
        chunkRequest.setParseContactInfo(requestDTO.getParseContactInfo());
        chunkRequest.setParseEducation(requestDTO.getParseEducation());
        chunkRequest.setParseWorkExperience(requestDTO.getParseWorkExperience());
        chunkRequest.setParseProjectExperience(requestDTO.getParseProjectExperience());
        chunkRequest.setParseSkills(requestDTO.getParseSkills());
        chunkRequest.setParseTraining(requestDTO.getParseTraining());
        chunkRequest.setParseLanguages(requestDTO.getParseLanguages());
        chunkRequest.setParseCertificates(requestDTO.getParseCertificates());
        chunkRequest.setParseAwards(requestDTO.getParseAwards());
        chunkRequest.setMaxConcurrency(Math.min(10, chunk.length));
        chunkRequest.setTimeoutSeconds(requestDTO.getTimeoutSeconds());

        try {
            // 调用现有的批量处理服务
            var result = batchResumeParseService.batchParseResumes(chunkRequest);
            
            // 更新进度
            progressTracker.recordChunkProcessed(chunk.length, 
                    result.getStatistics().getSuccessCount(),
                    result.getStatistics().getFailureCount(),
                    result.getStatistics().getSkippedCount());
            
        } catch (Exception e) {
            log.error("处理文件块失败: taskId={}, chunkSize={}", taskId, chunk.length, e);
            progressTracker.recordChunkProcessed(chunk.length, 0, chunk.length, 0);
        }
    }

    /**
     * 上传文件到对象存储
     */
    private void uploadFilesToStorage(String taskId, MultipartFile[] files) {
        log.info("开始上传文件到对象存储: taskId={}, fileCount={}", taskId, files.length);
        
        // 这里可以实现文件上传到MinIO、OSS等对象存储
        // 暂时跳过实现
        
        log.info("文件上传完成: taskId={}", taskId);
    }

    /**
     * 创建异步任务
     */
    private AsyncBatchTaskVO createAsyncTask(String taskId, BatchResumeUploadRequestDTO requestDTO) {
        return AsyncBatchTaskVO.builder()
                .taskId(taskId)
                .status("SUBMITTED")
                .totalFiles(requestDTO.getFiles().length)
                .processedFiles(0)
                .successCount(0)
                .failureCount(0)
                .skippedCount(0)
                .progressPercent(0.0)
                .submitTime(LocalDateTime.now())
                .estimatedCompletionTime(calculateEstimatedCompletionTime(requestDTO.getFiles().length))
                .build();
    }

    /**
     * 保存任务到Redis
     */
    private void saveTaskToRedis(String taskId, AsyncBatchTaskVO taskVO) {
        redisTemplate.opsForValue().set(
                "async:batch:task:" + taskId, 
                taskVO, 
                24, TimeUnit.HOURS
        );
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, String status, String errorMessage) {
        try {
            AsyncBatchTaskVO task = getTaskStatus(taskId);
            task.setStatus(status);
            task.setLastUpdateTime(LocalDateTime.now());
            
            if ("FAILED".equals(status) && errorMessage != null) {
                task.setErrorMessage(errorMessage);
            }
            
            if ("COMPLETED".equals(status)) {
                task.setCompletionTime(LocalDateTime.now());
                task.setProgressPercent(100.0);
            }
            
            saveTaskToRedis(taskId, task);
            
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, status={}", taskId, status, e);
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "batch_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * 计算预估完成时间
     */
    private LocalDateTime calculateEstimatedCompletionTime(int fileCount) {
        // 假设每个文件平均处理时间30秒，考虑并发处理
        int estimatedSeconds = (fileCount * 30) / 10; // 假设10个并发
        return LocalDateTime.now().plusSeconds(estimatedSeconds);
    }
}
