package com.tinyzk.user.center.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tinyzk.user.center.dto.BatchProgressMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 批量处理进度通知服务
 * 支持WebSocket实时推送和Redis持久化
 */
@Slf4j
@Service
public class BatchProgressNotificationService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 活跃的进度跟踪器
    private final ConcurrentHashMap<String, BatchProgressMessage> activeProgress = new ConcurrentHashMap<>();

    // 定时推送服务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    /**
     * 初始化进度跟踪
     */
    public void initializeProgress(String batchId, int totalFiles) {
        BatchProgressMessage progress = BatchProgressMessage.builder()
                .batchId(batchId)
                .status("INITIALIZED")
                .totalFiles(totalFiles)
                .processedFiles(0)
                .successCount(0)
                .failureCount(0)
                .skippedCount(0)
                .progressPercent(0.0)
                .estimatedTimeRemaining(0L)
                .currentFileName("")
                .lastUpdateTime(LocalDateTime.now())
                .build();

        activeProgress.put(batchId, progress);
        notifyProgress(batchId, progress);
    }

    /**
     * 更新文件处理进度
     */
    public void updateFileProgress(String batchId, String fileName, boolean success, boolean skipped, long processingTimeMs) {
        BatchProgressMessage progress = activeProgress.get(batchId);
        if (progress == null) {
            log.warn("进度跟踪器不存在: batchId={}", batchId);
            return;
        }

        synchronized (progress) {
            // 更新计数器
            progress.setProcessedFiles(progress.getProcessedFiles() + 1);
            if (skipped) {
                progress.setSkippedCount(progress.getSkippedCount() + 1);
            } else if (success) {
                progress.setSuccessCount(progress.getSuccessCount() + 1);
            } else {
                progress.setFailureCount(progress.getFailureCount() + 1);
            }

            // 更新进度百分比
            double progressPercent = (double) progress.getProcessedFiles() / progress.getTotalFiles() * 100;
            progress.setProgressPercent(progressPercent);

            // 更新当前处理文件
            progress.setCurrentFileName(fileName);

            // 计算预估剩余时间
            long estimatedTimeRemaining = calculateEstimatedTimeRemaining(progress, processingTimeMs);
            progress.setEstimatedTimeRemaining(estimatedTimeRemaining);

            // 更新时间戳
            progress.setLastUpdateTime(LocalDateTime.now());

            // 更新状态
            if (progress.getProcessedFiles() >= progress.getTotalFiles()) {
                progress.setStatus("COMPLETED");
                progress.setEstimatedTimeRemaining(0L);
            } else {
                progress.setStatus("PROCESSING");
            }
        }

        // 推送进度更新
        notifyProgress(batchId, progress);
    }

    /**
     * 批量更新进度
     */
    public void updateBatchProgress(String batchId, int processedCount, int successCount, int failureCount, int skippedCount) {
        BatchProgressMessage progress = activeProgress.get(batchId);
        if (progress == null) {
            log.warn("进度跟踪器不存在: batchId={}", batchId);
            return;
        }

        synchronized (progress) {
            progress.setProcessedFiles(progress.getProcessedFiles() + processedCount);
            progress.setSuccessCount(progress.getSuccessCount() + successCount);
            progress.setFailureCount(progress.getFailureCount() + failureCount);
            progress.setSkippedCount(progress.getSkippedCount() + skippedCount);

            // 更新进度百分比
            double progressPercent = (double) progress.getProcessedFiles() / progress.getTotalFiles() * 100;
            progress.setProgressPercent(progressPercent);

            // 更新时间戳
            progress.setLastUpdateTime(LocalDateTime.now());

            // 更新状态
            if (progress.getProcessedFiles() >= progress.getTotalFiles()) {
                progress.setStatus("COMPLETED");
                progress.setEstimatedTimeRemaining(0L);
            } else {
                progress.setStatus("PROCESSING");
            }
        }

        // 推送进度更新
        notifyProgress(batchId, progress);
    }

    /**
     * 设置任务状态
     */
    public void setTaskStatus(String batchId, String status, String errorMessage) {
        BatchProgressMessage progress = activeProgress.get(batchId);
        if (progress == null) {
            log.warn("进度跟踪器不存在: batchId={}", batchId);
            return;
        }

        synchronized (progress) {
            progress.setStatus(status);
            progress.setLastUpdateTime(LocalDateTime.now());
            
            if ("FAILED".equals(status) && errorMessage != null) {
                progress.setErrorMessage(errorMessage);
            }
        }

        notifyProgress(batchId, progress);

        // 如果任务完成或失败，清理活跃进度
        if ("COMPLETED".equals(status) || "FAILED".equals(status)) {
            scheduler.schedule(() -> activeProgress.remove(batchId), 5, TimeUnit.MINUTES);
        }
    }

    /**
     * 推送进度通知
     */
    public void notifyProgress(String batchId, BatchProgressMessage progress) {
        try {
            // 1. 保存到Redis
            redisTemplate.opsForValue().set(
                    "batch:progress:" + batchId,
                    progress,
                    Duration.ofHours(24)
            );

            // 2. WebSocket推送到特定主题
            messagingTemplate.convertAndSend(
                    "/topic/batch-progress/" + batchId,
                    progress
            );

            // 3. 广播到所有监听批量进度的客户端
            messagingTemplate.convertAndSend(
                    "/topic/batch-progress",
                    progress
            );

            // 4. 记录关键进度日志
            if (progress.getProcessedFiles() % 10 == 0 || "COMPLETED".equals(progress.getStatus()) || "FAILED".equals(progress.getStatus())) {
                log.info("批量处理进度更新: batchId={}, status={}, progress={}%, processed={}/{}, success={}, failure={}, skipped={}",
                        batchId, progress.getStatus(), String.format("%.1f", progress.getProgressPercent()),
                        progress.getProcessedFiles(), progress.getTotalFiles(),
                        progress.getSuccessCount(), progress.getFailureCount(), progress.getSkippedCount());
            }

        } catch (Exception e) {
            log.error("推送进度通知失败: batchId={}", batchId, e);
        }
    }

    /**
     * 获取进度信息
     */
    public BatchProgressMessage getProgress(String batchId) {
        // 先从内存获取
        BatchProgressMessage progress = activeProgress.get(batchId);
        if (progress != null) {
            return progress;
        }

        // 从Redis获取
        try {
            return (BatchProgressMessage) redisTemplate.opsForValue().get("batch:progress:" + batchId);
        } catch (Exception e) {
            log.error("获取进度信息失败: batchId={}", batchId, e);
            return null;
        }
    }

    /**
     * 计算预估剩余时间
     */
    private long calculateEstimatedTimeRemaining(BatchProgressMessage progress, long lastProcessingTimeMs) {
        if (progress.getProcessedFiles() == 0) {
            return 0L;
        }

        // 计算平均处理时间
        long totalProcessingTime = progress.getProcessedFiles() * lastProcessingTimeMs; // 简化计算
        long avgProcessingTime = totalProcessingTime / progress.getProcessedFiles();

        // 计算剩余文件数
        int remainingFiles = progress.getTotalFiles() - progress.getProcessedFiles();

        // 考虑并发处理，假设有10个并发
        int concurrency = 10;
        return (remainingFiles * avgProcessingTime) / concurrency;
    }

    /**
     * 清理过期的进度信息
     */
    public void cleanupExpiredProgress() {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(24);
        
        activeProgress.entrySet().removeIf(entry -> {
            BatchProgressMessage progress = entry.getValue();
            return progress.getLastUpdateTime().isBefore(cutoffTime);
        });
    }

    /**
     * 启动定时清理任务
     */
    public void startCleanupTask() {
        scheduler.scheduleAtFixedRate(this::cleanupExpiredProgress, 1, 1, TimeUnit.HOURS);
    }
}
