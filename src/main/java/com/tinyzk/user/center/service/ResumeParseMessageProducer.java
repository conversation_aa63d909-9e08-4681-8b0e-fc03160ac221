package com.tinyzk.user.center.service;

import com.tinyzk.user.center.config.RabbitMQConfig;
import com.tinyzk.user.center.dto.ResumeParseTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 简历解析消息生产者
 * 负责将批量任务分解为子任务并发送到消息队列
 */
@Slf4j
@Service
public class ResumeParseMessageProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送普通批量解析任务
     *
     * @param taskMessage 任务消息
     */
    public void sendBatchParseTask(ResumeParseTaskMessage taskMessage) {
        try {
            // 生成关联数据用于确认回调
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            log.info("发送批量解析任务到队列: taskId={}, subTaskId={}, fileCount={}", 
                    taskMessage.getTaskId(), taskMessage.getSubTaskId(), taskMessage.getFileUrls().size());

            // 发送消息到普通队列
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.RESUME_PARSE_EXCHANGE,
                    RabbitMQConfig.RESUME_PARSE_ROUTING_KEY,
                    taskMessage,
                    message -> {
                        MessageProperties properties = message.getMessageProperties();
                        
                        // 设置消息属性
                        properties.setMessageId(UUID.randomUUID().toString());
                        properties.setTimestamp(java.util.Date.from(
                                LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
                        properties.setExpiration("3600000"); // 1小时过期
                        
                        // 设置自定义头部信息
                        properties.setHeader("taskId", taskMessage.getTaskId());
                        properties.setHeader("subTaskId", taskMessage.getSubTaskId());
                        properties.setHeader("fileCount", taskMessage.getFileUrls().size());
                        properties.setHeader("retryCount", 0);
                        
                        return message;
                    },
                    correlationData
            );

            log.info("批量解析任务发送成功: correlationId={}", correlationData.getId());

        } catch (Exception e) {
            log.error("发送批量解析任务失败: taskId={}, subTaskId={}", 
                    taskMessage.getTaskId(), taskMessage.getSubTaskId(), e);
            throw new RuntimeException("消息发送失败", e);
        }
    }

    /**
     * 发送高优先级任务
     *
     * @param taskMessage 任务消息
     * @param priority    优先级 (1-10, 10最高)
     */
    public void sendPriorityParseTask(ResumeParseTaskMessage taskMessage, int priority) {
        try {
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            log.info("发送高优先级解析任务: taskId={}, subTaskId={}, priority={}", 
                    taskMessage.getTaskId(), taskMessage.getSubTaskId(), priority);

            // 发送消息到优先级队列
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.RESUME_PARSE_EXCHANGE,
                    RabbitMQConfig.RESUME_PARSE_PRIORITY_ROUTING_KEY,
                    taskMessage,
                    message -> {
                        MessageProperties properties = message.getMessageProperties();
                        
                        // 设置消息属性
                        properties.setMessageId(UUID.randomUUID().toString());
                        properties.setTimestamp(java.util.Date.from(
                                LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
                        properties.setExpiration("1800000"); // 30分钟过期
                        properties.setPriority(priority);    // 设置优先级
                        
                        // 设置自定义头部信息
                        properties.setHeader("taskId", taskMessage.getTaskId());
                        properties.setHeader("subTaskId", taskMessage.getSubTaskId());
                        properties.setHeader("priority", priority);
                        properties.setHeader("retryCount", 0);
                        
                        return message;
                    },
                    correlationData
            );

            log.info("高优先级任务发送成功: correlationId={}, priority={}", 
                    correlationData.getId(), priority);

        } catch (Exception e) {
            log.error("发送高优先级任务失败: taskId={}, subTaskId={}, priority={}", 
                    taskMessage.getTaskId(), taskMessage.getSubTaskId(), priority, e);
            throw new RuntimeException("优先级消息发送失败", e);
        }
    }

    /**
     * 批量发送子任务
     *
     * @param mainTaskId 主任务ID
     * @param subTasks   子任务列表
     */
    public void sendBatchSubTasks(String mainTaskId, java.util.List<ResumeParseTaskMessage> subTasks) {
        log.info("开始批量发送子任务: mainTaskId={}, subTaskCount={}", mainTaskId, subTasks.size());

        int successCount = 0;
        int failureCount = 0;

        for (ResumeParseTaskMessage subTask : subTasks) {
            try {
                // 根据任务类型选择队列
                if (subTask.isHighPriority()) {
                    sendPriorityParseTask(subTask, subTask.getPriority());
                } else {
                    sendBatchParseTask(subTask);
                }
                successCount++;
                
                // 避免消息发送过快，适当延迟
                Thread.sleep(10);
                
            } catch (Exception e) {
                log.error("发送子任务失败: subTaskId={}", subTask.getSubTaskId(), e);
                failureCount++;
            }
        }

        log.info("批量子任务发送完成: mainTaskId={}, 成功={}, 失败={}", 
                mainTaskId, successCount, failureCount);

        if (failureCount > 0) {
            throw new RuntimeException(String.format("部分子任务发送失败: 成功=%d, 失败=%d", 
                    successCount, failureCount));
        }
    }

    /**
     * 发送延迟任务
     *
     * @param taskMessage 任务消息
     * @param delaySeconds 延迟秒数
     */
    public void sendDelayedTask(ResumeParseTaskMessage taskMessage, int delaySeconds) {
        try {
            CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
            
            log.info("发送延迟解析任务: taskId={}, subTaskId={}, delaySeconds={}", 
                    taskMessage.getTaskId(), taskMessage.getSubTaskId(), delaySeconds);

            // 使用TTL实现延迟队列
            rabbitTemplate.convertAndSend(
                    RabbitMQConfig.RESUME_PARSE_EXCHANGE,
                    RabbitMQConfig.RESUME_PARSE_ROUTING_KEY,
                    taskMessage,
                    message -> {
                        MessageProperties properties = message.getMessageProperties();
                        
                        // 设置延迟时间
                        properties.setExpiration(String.valueOf(delaySeconds * 1000));
                        properties.setMessageId(UUID.randomUUID().toString());
                        properties.setTimestamp(java.util.Date.from(
                                LocalDateTime.now().atZone(java.time.ZoneId.systemDefault()).toInstant()));
                        
                        // 设置自定义头部信息
                        properties.setHeader("taskId", taskMessage.getTaskId());
                        properties.setHeader("subTaskId", taskMessage.getSubTaskId());
                        properties.setHeader("delaySeconds", delaySeconds);
                        properties.setHeader("scheduledTime", 
                                LocalDateTime.now().plusSeconds(delaySeconds).toString());
                        
                        return message;
                    },
                    correlationData
            );

            log.info("延迟任务发送成功: correlationId={}, delaySeconds={}", 
                    correlationData.getId(), delaySeconds);

        } catch (Exception e) {
            log.error("发送延迟任务失败: taskId={}, subTaskId={}, delaySeconds={}", 
                    taskMessage.getTaskId(), taskMessage.getSubTaskId(), delaySeconds, e);
            throw new RuntimeException("延迟消息发送失败", e);
        }
    }

    /**
     * 获取队列信息
     */
    public QueueInfo getQueueInfo(String queueName) {
        try {
            // 这里可以通过RabbitMQ Management API获取队列信息
            // 或者使用RabbitAdmin
            return QueueInfo.builder()
                    .queueName(queueName)
                    .messageCount(0)  // 实际实现中需要调用API获取
                    .consumerCount(0)
                    .build();
        } catch (Exception e) {
            log.error("获取队列信息失败: queueName={}", queueName, e);
            return null;
        }
    }

    /**
     * 队列信息VO
     */
    @lombok.Data
    @lombok.Builder
    public static class QueueInfo {
        private String queueName;
        private long messageCount;
        private int consumerCount;
    }
}
