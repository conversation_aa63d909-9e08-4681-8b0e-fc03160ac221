package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.result.Result;
import com.tinyzk.user.center.service.MessageQueueManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 消息队列监控控制器
 * 提供队列状态监控和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/mq")
@Tag(name = "消息队列管理", description = "RabbitMQ队列监控和管理接口")
public class MessageQueueController {

    @Autowired
    private MessageQueueManagementService mqManagementService;

    /**
     * 获取队列统计信息
     */
    @GetMapping("/statistics")
    @Operation(summary = "获取队列统计信息", description = "获取所有队列的消息数量和消费者数量")
    public Result<MessageQueueManagementService.QueueStatistics> getQueueStatistics() {
        try {
            MessageQueueManagementService.QueueStatistics stats = mqManagementService.getQueueStatistics();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取队列统计信息失败", e);
            return Result.error("获取队列统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "获取队列健康状态", description = "检查所有队列的健康状况")
    public Result<MessageQueueManagementService.QueueHealthStatus> getQueueHealth() {
        try {
            MessageQueueManagementService.QueueHealthStatus health = mqManagementService.getQueueHealthStatus();
            return Result.success(health);
        } catch (Exception e) {
            log.error("获取队列健康状态失败", e);
            return Result.error("获取队列健康状态失败: " + e.getMessage());
        }
    }

    /**
     * 清空指定队列
     */
    @DeleteMapping("/purge/{queueName}")
    @Operation(summary = "清空指定队列", description = "删除指定队列中的所有消息")
    public Result<Void> purgeQueue(
            @Parameter(description = "队列名称", required = true)
            @PathVariable String queueName) {
        try {
            boolean success = mqManagementService.purgeQueue(queueName);
            if (success) {
                return Result.success(null, "队列清空成功");
            } else {
                return Result.error("队列清空失败");
            }
        } catch (Exception e) {
            log.error("清空队列失败: queueName={}", queueName, e);
            return Result.error("清空队列失败: " + e.getMessage());
        }
    }

    /**
     * 发送测试消息
     */
    @PostMapping("/test-messages")
    @Operation(summary = "发送测试消息", description = "向队列发送指定数量的测试消息")
    public Result<Void> sendTestMessages(
            @Parameter(description = "消息数量", required = true)
            @RequestParam int messageCount) {
        try {
            if (messageCount <= 0 || messageCount > 1000) {
                return Result.error("消息数量必须在1-1000之间");
            }

            mqManagementService.sendTestMessage(messageCount);
            return Result.success(null, "测试消息发送成功");
        } catch (Exception e) {
            log.error("发送测试消息失败: messageCount={}", messageCount, e);
            return Result.error("发送测试消息失败: " + e.getMessage());
        }
    }

    /**
     * 重新处理死信队列消息
     */
    @PostMapping("/reprocess-dlq")
    @Operation(summary = "重新处理死信队列", description = "将死信队列中的消息重新发送到正常队列")
    public Result<Void> reprocessDeadLetterQueue(
            @Parameter(description = "最大处理数量", required = false)
            @RequestParam(defaultValue = "100") int maxCount) {
        try {
            mqManagementService.reprocessDeadLetterMessages(maxCount);
            return Result.success(null, "死信队列重处理完成");
        } catch (Exception e) {
            log.error("重新处理死信队列失败: maxCount={}", maxCount, e);
            return Result.error("重新处理死信队列失败: " + e.getMessage());
        }
    }

    /**
     * 动态调整消费者数量
     */
    @PostMapping("/adjust-consumers")
    @Operation(summary = "动态调整消费者数量", description = "调整指定队列的消费者数量")
    public Result<Void> adjustConsumerCount(
            @Parameter(description = "队列名称", required = true)
            @RequestParam String queueName,
            @Parameter(description = "目标消费者数量", required = true)
            @RequestParam int targetCount) {
        try {
            if (targetCount < 0 || targetCount > 50) {
                return Result.error("消费者数量必须在0-50之间");
            }

            mqManagementService.adjustConsumerCount(queueName, targetCount);
            return Result.success(null, "消费者数量调整完成");
        } catch (Exception e) {
            log.error("调整消费者数量失败: queueName={}, targetCount={}", queueName, targetCount, e);
            return Result.error("调整消费者数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取队列详细信息
     */
    @GetMapping("/queue-info")
    @Operation(summary = "获取队列详细信息", description = "获取所有队列的详细配置和状态信息")
    public Result<QueueInfoVO> getQueueInfo() {
        try {
            MessageQueueManagementService.QueueStatistics stats = mqManagementService.getQueueStatistics();
            MessageQueueManagementService.QueueHealthStatus health = mqManagementService.getQueueHealthStatus();

            QueueInfoVO queueInfo = QueueInfoVO.builder()
                    .statistics(stats)
                    .healthStatus(health)
                    .timestamp(java.time.LocalDateTime.now())
                    .build();

            return Result.success(queueInfo);
        } catch (Exception e) {
            log.error("获取队列详细信息失败", e);
            return Result.error("获取队列详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 队列信息VO
     */
    @lombok.Data
    @lombok.Builder
    public static class QueueInfoVO {
        private MessageQueueManagementService.QueueStatistics statistics;
        private MessageQueueManagementService.QueueHealthStatus healthStatus;
        private java.time.LocalDateTime timestamp;
    }
}
