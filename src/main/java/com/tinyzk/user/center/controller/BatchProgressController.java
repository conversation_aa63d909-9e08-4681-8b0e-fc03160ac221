package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.result.Result;
import com.tinyzk.user.center.dto.BatchProgressMessage;
import com.tinyzk.user.center.service.BatchProgressNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 批量处理进度监控控制器
 * 提供WebSocket和SSE两种实时进度推送方式
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/batch-progress")
@Tag(name = "批量处理进度监控", description = "批量简历解析进度监控相关接口")
public class BatchProgressController {

    @Autowired
    private BatchProgressNotificationService progressNotificationService;

    // SSE连接管理
    private final ConcurrentHashMap<String, SseEmitter> sseEmitters = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    /**
     * 获取批量处理进度
     */
    @GetMapping("/{batchId}")
    @Operation(summary = "获取批量处理进度", description = "获取指定批次的处理进度信息")
    public Result<BatchProgressMessage> getProgress(
            @Parameter(description = "批次ID", required = true)
            @PathVariable String batchId) {
        
        try {
            BatchProgressMessage progress = progressNotificationService.getProgress(batchId);
            
            if (progress == null) {
                return Result.error("批次不存在或已过期: " + batchId);
            }
            
            return Result.success(progress);
            
        } catch (Exception e) {
            log.error("获取批量处理进度失败: batchId={}", batchId, e);
            return Result.error("获取进度失败: " + e.getMessage());
        }
    }

    /**
     * SSE实时进度流
     */
    @GetMapping(value = "/stream/{batchId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "SSE实时进度流", description = "通过Server-Sent Events获取实时进度更新")
    public SseEmitter streamProgress(
            @Parameter(description = "批次ID", required = true)
            @PathVariable String batchId) {
        
        log.info("建立SSE连接: batchId={}", batchId);
        
        // 创建SSE发射器，设置超时时间为30分钟
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);
        
        // 保存连接
        sseEmitters.put(batchId, emitter);
        
        // 设置连接完成和超时回调
        emitter.onCompletion(() -> {
            log.info("SSE连接完成: batchId={}", batchId);
            sseEmitters.remove(batchId);
        });
        
        emitter.onTimeout(() -> {
            log.info("SSE连接超时: batchId={}", batchId);
            sseEmitters.remove(batchId);
        });
        
        emitter.onError((throwable) -> {
            log.error("SSE连接错误: batchId={}", batchId, throwable);
            sseEmitters.remove(batchId);
        });
        
        // 立即发送当前进度
        try {
            BatchProgressMessage currentProgress = progressNotificationService.getProgress(batchId);
            if (currentProgress != null) {
                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(currentProgress));
            }
        } catch (IOException e) {
            log.error("发送初始进度失败: batchId={}", batchId, e);
        }
        
        // 启动定时推送任务
        startProgressStreaming(batchId, emitter);
        
        return emitter;
    }

    /**
     * 启动进度流推送
     */
    private void startProgressStreaming(String batchId, SseEmitter emitter) {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                BatchProgressMessage progress = progressNotificationService.getProgress(batchId);
                
                if (progress != null) {
                    emitter.send(SseEmitter.event()
                            .name("progress")
                            .data(progress));
                    
                    // 如果任务完成，关闭连接
                    if ("COMPLETED".equals(progress.getStatus()) || "FAILED".equals(progress.getStatus())) {
                        emitter.complete();
                        sseEmitters.remove(batchId);
                    }
                } else {
                    // 进度信息不存在，可能任务已过期
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("批次不存在或已过期"));
                    emitter.complete();
                    sseEmitters.remove(batchId);
                }
                
            } catch (IOException e) {
                log.error("SSE推送进度失败: batchId={}", batchId, e);
                emitter.completeWithError(e);
                sseEmitters.remove(batchId);
            } catch (Exception e) {
                log.error("获取进度信息失败: batchId={}", batchId, e);
            }
        }, 0, 2, TimeUnit.SECONDS); // 每2秒推送一次
    }

    /**
     * 取消进度监控
     */
    @DeleteMapping("/{batchId}")
    @Operation(summary = "取消进度监控", description = "取消指定批次的进度监控")
    public Result<Void> cancelProgressMonitoring(
            @Parameter(description = "批次ID", required = true)
            @PathVariable String batchId) {
        
        try {
            SseEmitter emitter = sseEmitters.remove(batchId);
            if (emitter != null) {
                emitter.complete();
                log.info("取消进度监控: batchId={}", batchId);
            }
            
            return Result.success();
            
        } catch (Exception e) {
            log.error("取消进度监控失败: batchId={}", batchId, e);
            return Result.error("取消监控失败: " + e.getMessage());
        }
    }

    /**
     * 获取活跃的监控连接数
     */
    @GetMapping("/active-connections")
    @Operation(summary = "获取活跃连接数", description = "获取当前活跃的SSE连接数量")
    public Result<Integer> getActiveConnections() {
        return Result.success(sseEmitters.size());
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查进度监控服务状态")
    public Result<String> healthCheck() {
        return Result.success("进度监控服务正常运行，活跃连接数: " + sseEmitters.size());
    }
}
