package com.tinyzk.user.center.controller;

import com.tinyzk.user.center.common.result.Result;
import com.tinyzk.user.center.dto.BatchResumeUploadRequestDTO;
import com.tinyzk.user.center.vo.AsyncBatchTaskVO;
import com.tinyzk.user.center.service.AsyncBatchResumeParseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 异步批量简历解析控制器
 * 支持阿里云OSS加密上传
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/async-batch")
@Tag(name = "异步批量简历解析", description = "支持大规模批量处理和实时进度监控")
public class AsyncBatchResumeController {

    @Autowired
    private AsyncBatchResumeParseService asyncBatchResumeParseService;

    /**
     * 提交异步批量简历解析任务
     */
    @PostMapping(value = "/submit", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "提交异步批量简历解析任务", 
               description = "支持大规模文件批量处理，文件将加密上传到阿里云OSS")
    public Result<AsyncBatchTaskVO> submitAsyncBatch(
            @Parameter(description = "简历文件数组", required = true)
            @RequestParam("files") MultipartFile[] files,

            @Parameter(description = "是否覆盖现有数据", example = "false")
            @RequestParam(value = "overwriteExisting", defaultValue = "false") Boolean overwriteExisting,

            @Parameter(description = "是否解析基本信息", example = "true")
            @RequestParam(value = "parseBasicInfo", defaultValue = "true") Boolean parseBasicInfo,

            @Parameter(description = "是否解析联系方式", example = "true")
            @RequestParam(value = "parseContactInfo", defaultValue = "true") Boolean parseContactInfo,

            @Parameter(description = "是否解析教育经历", example = "true")
            @RequestParam(value = "parseEducation", defaultValue = "true") Boolean parseEducation,

            @Parameter(description = "是否解析工作经历", example = "true")
            @RequestParam(value = "parseWorkExperience", defaultValue = "true") Boolean parseWorkExperience,

            @Parameter(description = "是否解析项目经历", example = "true")
            @RequestParam(value = "parseProjectExperience", defaultValue = "true") Boolean parseProjectExperience,

            @Parameter(description = "是否解析技能信息", example = "true")
            @RequestParam(value = "parseSkills", defaultValue = "true") Boolean parseSkills,

            @Parameter(description = "是否解析培训经历", example = "true")
            @RequestParam(value = "parseTraining", defaultValue = "true") Boolean parseTraining,

            @Parameter(description = "是否解析语言能力", example = "true")
            @RequestParam(value = "parseLanguages", defaultValue = "true") Boolean parseLanguages,

            @Parameter(description = "是否解析证书信息", example = "true")
            @RequestParam(value = "parseCertificates", defaultValue = "true") Boolean parseCertificates,

            @Parameter(description = "是否解析获奖记录", example = "true")
            @RequestParam(value = "parseAwards", defaultValue = "true") Boolean parseAwards,

            @Parameter(description = "最大并发处理数量", example = "20")
            @RequestParam(value = "maxConcurrency", defaultValue = "20") Integer maxConcurrency,

            @Parameter(description = "单个文件处理超时时间（秒）", example = "120")
            @RequestParam(value = "timeoutSeconds", defaultValue = "120") Integer timeoutSeconds) {

        log.info("提交异步批量简历解析任务: fileCount={}, maxConcurrency={}", 
                files.length, maxConcurrency);

        try {
            // 构建请求DTO
            BatchResumeUploadRequestDTO requestDTO = new BatchResumeUploadRequestDTO();
            requestDTO.setFiles(files);
            requestDTO.setOverwriteExisting(overwriteExisting);
            requestDTO.setParseBasicInfo(parseBasicInfo);
            requestDTO.setParseContactInfo(parseContactInfo);
            requestDTO.setParseEducation(parseEducation);
            requestDTO.setParseWorkExperience(parseWorkExperience);
            requestDTO.setParseProjectExperience(parseProjectExperience);
            requestDTO.setParseSkills(parseSkills);
            requestDTO.setParseTraining(parseTraining);
            requestDTO.setParseLanguages(parseLanguages);
            requestDTO.setParseCertificates(parseCertificates);
            requestDTO.setParseAwards(parseAwards);
            requestDTO.setMaxConcurrency(maxConcurrency);
            requestDTO.setTimeoutSeconds(timeoutSeconds);

            // 提交异步任务
            AsyncBatchTaskVO taskVO = asyncBatchResumeParseService.submitAsyncBatch(requestDTO);

            return Result.success(taskVO, "异步批量解析任务提交成功");

        } catch (Exception e) {
            log.error("提交异步批量解析任务失败", e);
            return Result.error("任务提交失败: " + e.getMessage());
        }
    }

    /**
     * 获取异步任务状态
     */
    @GetMapping("/status/{taskId}")
    @Operation(summary = "获取异步任务状态", description = "查询异步批量处理任务的当前状态")
    public Result<AsyncBatchTaskVO> getTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable String taskId) {

        try {
            AsyncBatchTaskVO taskVO = asyncBatchResumeParseService.getTaskStatus(taskId);
            return Result.success(taskVO);

        } catch (Exception e) {
            log.error("获取任务状态失败: taskId={}", taskId, e);
            return Result.error("获取任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取OSS上传配置信息
     */
    @GetMapping("/oss-config")
    @Operation(summary = "获取OSS上传配置", description = "获取阿里云OSS上传相关配置信息")
    public Result<OssConfigVO> getOssConfig() {
        try {
            OssConfigVO configVO = OssConfigVO.builder()
                    .encryptionEnabled(true)
                    .encryptionAlgorithm("AES256")
                    .maxFileSize("50MB")
                    .supportedFormats(new String[]{"pdf", "doc", "docx", "txt"})
                    .build();

            return Result.success(configVO, "OSS配置获取成功");

        } catch (Exception e) {
            log.error("获取OSS配置失败", e);
            return Result.error("获取OSS配置失败: " + e.getMessage());
        }
    }

    /**
     * OSS配置信息VO
     */
    @lombok.Data
    @lombok.Builder
    public static class OssConfigVO {
        private Boolean encryptionEnabled;
        private String encryptionAlgorithm;
        private String maxFileSize;
        private String[] supportedFormats;
    }
}
