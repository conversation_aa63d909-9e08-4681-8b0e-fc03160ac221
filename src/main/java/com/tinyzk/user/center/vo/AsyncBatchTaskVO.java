package com.tinyzk.user.center.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 异步批量任务VO
 */
@Data
@Builder
@Schema(description = "异步批量任务信息")
public class AsyncBatchTaskVO {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "任务状态")
    private String status;

    @Schema(description = "总文件数")
    private Integer totalFiles;

    @Schema(description = "已处理文件数")
    private Integer processedFiles;

    @Schema(description = "成功数量")
    private Integer successCount;

    @Schema(description = "失败数量")
    private Integer failureCount;

    @Schema(description = "跳过数量")
    private Integer skippedCount;

    @Schema(description = "进度百分比")
    private Double progressPercent;

    @Schema(description = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completionTime;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "预估完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime estimatedCompletionTime;

    @Schema(description = "错误信息")
    private String errorMessage;
}
