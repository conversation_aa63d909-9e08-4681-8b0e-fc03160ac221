package com.tinyzk.user.center.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 简历解析任务消息DTO
 * 用于RabbitMQ消息传递
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResumeParseTaskMessage {

    /**
     * 主任务ID
     */
    private String taskId;

    /**
     * 子任务ID
     */
    private String subTaskId;

    /**
     * 文件URL列表（OSS存储路径）
     */
    private List<String> fileUrls;

    /**
     * 解析选项
     */
    private ParseOptions parseOptions;

    /**
     * 任务优先级 (1-10, 10最高)
     */
    private Integer priority;

    /**
     * 是否高优先级任务
     */
    private Boolean highPriority;

    /**
     * 任务创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 任务超时时间（秒）
     */
    private Integer timeoutSeconds;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 任务元数据
     */
    private Map<String, Object> metadata;

    /**
     * 回调URL（可选）
     */
    private String callbackUrl;

    /**
     * 解析选项内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParseOptions {
        
        /**
         * 是否覆盖现有数据
         */
        private Boolean overwriteExisting;

        /**
         * 是否解析基本信息
         */
        private Boolean parseBasicInfo;

        /**
         * 是否解析联系方式
         */
        private Boolean parseContactInfo;

        /**
         * 是否解析教育经历
         */
        private Boolean parseEducation;

        /**
         * 是否解析工作经历
         */
        private Boolean parseWorkExperience;

        /**
         * 是否解析项目经历
         */
        private Boolean parseProjectExperience;

        /**
         * 是否解析技能信息
         */
        private Boolean parseSkills;

        /**
         * 是否解析培训经历
         */
        private Boolean parseTraining;

        /**
         * 是否解析语言能力
         */
        private Boolean parseLanguages;

        /**
         * 是否解析证书信息
         */
        private Boolean parseCertificates;

        /**
         * 是否解析获奖记录
         */
        private Boolean parseAwards;

        /**
         * 最大并发处理数量
         */
        private Integer maxConcurrency;

        /**
         * 单个文件处理超时时间（秒）
         */
        private Integer fileTimeoutSeconds;
    }

    /**
     * 创建普通优先级任务
     */
    public static ResumeParseTaskMessage createNormalTask(String taskId, String subTaskId, 
                                                         List<String> fileUrls, ParseOptions options) {
        return ResumeParseTaskMessage.builder()
                .taskId(taskId)
                .subTaskId(subTaskId)
                .fileUrls(fileUrls)
                .parseOptions(options)
                .priority(5)
                .highPriority(false)
                .createTime(LocalDateTime.now())
                .timeoutSeconds(3600) // 1小时
                .maxRetries(3)
                .build();
    }

    /**
     * 创建高优先级任务
     */
    public static ResumeParseTaskMessage createPriorityTask(String taskId, String subTaskId, 
                                                           List<String> fileUrls, ParseOptions options, 
                                                           int priority) {
        return ResumeParseTaskMessage.builder()
                .taskId(taskId)
                .subTaskId(subTaskId)
                .fileUrls(fileUrls)
                .parseOptions(options)
                .priority(Math.max(1, Math.min(10, priority))) // 确保在1-10范围内
                .highPriority(true)
                .createTime(LocalDateTime.now())
                .timeoutSeconds(1800) // 30分钟
                .maxRetries(1) // 高优先级任务减少重试次数
                .build();
    }

    /**
     * 创建延迟任务
     */
    public static ResumeParseTaskMessage createDelayedTask(String taskId, String subTaskId, 
                                                          List<String> fileUrls, ParseOptions options, 
                                                          int delaySeconds) {
        return ResumeParseTaskMessage.builder()
                .taskId(taskId)
                .subTaskId(subTaskId)
                .fileUrls(fileUrls)
                .parseOptions(options)
                .priority(3)
                .highPriority(false)
                .createTime(LocalDateTime.now())
                .timeoutSeconds(3600)
                .maxRetries(3)
                .metadata(Map.of("delaySeconds", delaySeconds, "scheduledTime", 
                        LocalDateTime.now().plusSeconds(delaySeconds).toString()))
                .build();
    }

    /**
     * 验证消息有效性
     */
    public boolean isValid() {
        return taskId != null && !taskId.trim().isEmpty() &&
               subTaskId != null && !subTaskId.trim().isEmpty() &&
               fileUrls != null && !fileUrls.isEmpty() &&
               parseOptions != null &&
               priority != null && priority >= 1 && priority <= 10 &&
               timeoutSeconds != null && timeoutSeconds > 0 &&
               maxRetries != null && maxRetries >= 0;
    }

    /**
     * 获取任务描述
     */
    public String getTaskDescription() {
        return String.format("Task[%s] SubTask[%s] Files[%d] Priority[%d] Timeout[%ds]",
                taskId, subTaskId, 
                fileUrls != null ? fileUrls.size() : 0, 
                priority, timeoutSeconds);
    }

    /**
     * 是否为批量任务
     */
    public boolean isBatchTask() {
        return fileUrls != null && fileUrls.size() > 1;
    }

    /**
     * 获取预估处理时间（秒）
     */
    public long getEstimatedProcessingTime() {
        if (fileUrls == null || fileUrls.isEmpty()) {
            return 0;
        }
        
        // 假设每个文件平均处理时间30秒
        long baseTime = fileUrls.size() * 30L;
        
        // 高优先级任务处理更快
        if (Boolean.TRUE.equals(highPriority)) {
            baseTime = (long) (baseTime * 0.8);
        }
        
        return baseTime;
    }
}
