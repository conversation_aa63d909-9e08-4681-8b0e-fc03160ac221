package com.tinyzk.user.center.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.RabbitListenerContainerFactory;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置类
 * 配置批量简历解析的消息队列
 */
@Slf4j
@Configuration
public class RabbitMQConfig {

    // 交换机名称
    public static final String RESUME_PARSE_EXCHANGE = "resume.parse.exchange";
    public static final String RESUME_PARSE_DLX_EXCHANGE = "resume.parse.dlx.exchange";

    // 队列名称
    public static final String RESUME_PARSE_QUEUE = "resume.parse.queue";
    public static final String RESUME_PARSE_DLQ = "resume.parse.dlq";
    public static final String RESUME_PARSE_PRIORITY_QUEUE = "resume.parse.priority.queue";

    // 路由键
    public static final String RESUME_PARSE_ROUTING_KEY = "resume.parse.batch";
    public static final String RESUME_PARSE_PRIORITY_ROUTING_KEY = "resume.parse.priority";
    public static final String RESUME_PARSE_DLQ_ROUTING_KEY = "resume.parse.dlq";

    /**
     * 消息转换器 - 使用JSON格式
     */
    @Bean
    public MessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    /**
     * RabbitTemplate配置
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter());
        
        // 启用发布确认
        template.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.debug("消息发送成功: correlationData={}", correlationData);
            } else {
                log.error("消息发送失败: correlationData={}, cause={}", correlationData, cause);
            }
        });
        
        // 启用返回确认
        template.setReturnsCallback(returned -> {
            log.error("消息被退回: message={}, replyCode={}, replyText={}, exchange={}, routingKey={}",
                    returned.getMessage(), returned.getReplyCode(), returned.getReplyText(),
                    returned.getExchange(), returned.getRoutingKey());
        });
        
        template.setMandatory(true);
        return template;
    }

    /**
     * 监听器容器工厂配置
     */
    @Bean
    public RabbitListenerContainerFactory<?> rabbitListenerContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter());
        
        // 并发配置
        factory.setConcurrentConsumers(5);          // 初始消费者数量
        factory.setMaxConcurrentConsumers(20);      // 最大消费者数量
        factory.setPrefetchCount(10);               // 预取消息数量
        
        // 重试配置
        factory.setDefaultRequeueRejected(false);   // 失败消息不重新入队
        
        // 确认模式
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);  // 手动确认
        
        log.info("RabbitMQ监听器容器工厂配置完成");
        return factory;
    }

    /**
     * 主交换机 - 直连交换机
     */
    @Bean
    public DirectExchange resumeParseExchange() {
        return ExchangeBuilder
                .directExchange(RESUME_PARSE_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 死信交换机
     */
    @Bean
    public DirectExchange resumeParseDlxExchange() {
        return ExchangeBuilder
                .directExchange(RESUME_PARSE_DLX_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * 普通处理队列
     */
    @Bean
    public Queue resumeParseQueue() {
        return QueueBuilder
                .durable(RESUME_PARSE_QUEUE)
                .withArgument("x-message-ttl", 3600000)                    // 消息TTL 1小时
                .withArgument("x-max-length", 10000)                       // 队列最大长度
                .withArgument("x-dead-letter-exchange", RESUME_PARSE_DLX_EXCHANGE)  // 死信交换机
                .withArgument("x-dead-letter-routing-key", RESUME_PARSE_DLQ_ROUTING_KEY)  // 死信路由键
                .build();
    }

    /**
     * 优先级处理队列
     */
    @Bean
    public Queue resumeParsePriorityQueue() {
        return QueueBuilder
                .durable(RESUME_PARSE_PRIORITY_QUEUE)
                .withArgument("x-max-priority", 10)                        // 最大优先级
                .withArgument("x-message-ttl", 1800000)                    // 消息TTL 30分钟
                .withArgument("x-max-length", 5000)                        // 队列最大长度
                .withArgument("x-dead-letter-exchange", RESUME_PARSE_DLX_EXCHANGE)
                .withArgument("x-dead-letter-routing-key", RESUME_PARSE_DLQ_ROUTING_KEY)
                .build();
    }

    /**
     * 死信队列
     */
    @Bean
    public Queue resumeParseDlq() {
        return QueueBuilder
                .durable(RESUME_PARSE_DLQ)
                .build();
    }

    /**
     * 绑定普通队列到交换机
     */
    @Bean
    public Binding resumeParseBinding() {
        return BindingBuilder
                .bind(resumeParseQueue())
                .to(resumeParseExchange())
                .with(RESUME_PARSE_ROUTING_KEY);
    }

    /**
     * 绑定优先级队列到交换机
     */
    @Bean
    public Binding resumeParsePriorityBinding() {
        return BindingBuilder
                .bind(resumeParsePriorityQueue())
                .to(resumeParseExchange())
                .with(RESUME_PARSE_PRIORITY_ROUTING_KEY);
    }

    /**
     * 绑定死信队列到死信交换机
     */
    @Bean
    public Binding resumeParseDlqBinding() {
        return BindingBuilder
                .bind(resumeParseDlq())
                .to(resumeParseDlxExchange())
                .with(RESUME_PARSE_DLQ_ROUTING_KEY);
    }
}
