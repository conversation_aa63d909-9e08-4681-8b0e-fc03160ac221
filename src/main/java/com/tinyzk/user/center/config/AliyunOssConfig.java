package com.tinyzk.user.center.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云OSS配置类
 * 支持加密上传和安全访问
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
public class AliyunOssConfig {

    /**
     * OSS访问域名端点
     */
    private String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";

    /**
     * 访问密钥ID (建议通过环境变量设置)
     */
    private String accessKeyId;

    /**
     * 访问密钥Secret (建议通过环境变量设置)
     */
    private String accessKeySecret;

    /**
     * 存储桶名称
     */
    private String bucketName = "user-center-files";

    /**
     * 文件存储根路径
     */
    private String basePath = "batch-resume/";

    /**
     * 是否启用HTTPS
     */
    private Boolean enableHttps = true;

    /**
     * 连接超时时间(毫秒)
     */
    private Integer connectionTimeout = 50000;

    /**
     * Socket超时时间(毫秒)
     */
    private Integer socketTimeout = 50000;

    /**
     * 最大连接数
     */
    private Integer maxConnections = 1024;

    /**
     * 最大错误重试次数
     */
    private Integer maxErrorRetry = 3;

    /**
     * 加密配置
     */
    private Encryption encryption = new Encryption();

    /**
     * 创建OSS客户端
     */
    @Bean
    public OSS ossClient() {
        try {
            // 优先使用环境变量中的凭证
            EnvironmentVariableCredentialsProvider credentialsProvider = 
                CredentialsProviderFactory.newEnvironmentVariableCredentialsProvider();

            // 如果环境变量中没有凭证，则使用配置文件中的
            if (accessKeyId != null && accessKeySecret != null) {
                log.info("使用配置文件中的OSS凭证");
                return new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            } else {
                log.info("使用环境变量中的OSS凭证");
                return new OSSClientBuilder().build(endpoint, credentialsProvider);
            }
        } catch (Exception e) {
            log.error("创建OSS客户端失败", e);
            throw new RuntimeException("OSS客户端初始化失败", e);
        }
    }

    /**
     * 加密配置类
     */
    @Data
    public static class Encryption {
        /**
         * 是否启用服务端加密
         */
        private Boolean enabled = true;

        /**
         * 加密算法类型
         * AES256: OSS完全托管加密
         * KMS: 使用阿里云KMS密钥管理
         * SM4: 国密SM4算法
         */
        private String algorithm = "AES256";

        /**
         * KMS密钥ID (当algorithm为KMS时使用)
         */
        private String kmsKeyId;

        /**
         * KMS加密上下文 (可选)
         */
        private String kmsEncryptionContext;

        /**
         * 是否启用客户端加密
         */
        private Boolean clientSideEnabled = false;

        /**
         * 客户端加密密钥
         */
        private String clientSideKey;

        /**
         * 客户端加密算法
         */
        private String clientSideAlgorithm = "AES/CTR/NoPadding";
    }

    /**
     * 获取完整的文件路径
     */
    public String getFullPath(String fileName) {
        return basePath + fileName;
    }

    /**
     * 获取文件访问URL
     */
    public String getFileUrl(String fileName) {
        String protocol = enableHttps ? "https" : "http";
        String domain = endpoint.replace("https://", "").replace("http://", "");
        return String.format("%s://%s.%s/%s", protocol, bucketName, domain, getFullPath(fileName));
    }

    /**
     * 验证配置
     */
    public void validateConfig() {
        if (bucketName == null || bucketName.trim().isEmpty()) {
            throw new IllegalArgumentException("OSS bucket name不能为空");
        }
        
        if (endpoint == null || endpoint.trim().isEmpty()) {
            throw new IllegalArgumentException("OSS endpoint不能为空");
        }

        // 如果启用KMS加密，验证KMS配置
        if (encryption.enabled && "KMS".equalsIgnoreCase(encryption.algorithm)) {
            if (encryption.kmsKeyId == null || encryption.kmsKeyId.trim().isEmpty()) {
                throw new IllegalArgumentException("启用KMS加密时，kmsKeyId不能为空");
            }
        }

        log.info("OSS配置验证通过: endpoint={}, bucket={}, encryption={}", 
                endpoint, bucketName, encryption.enabled ? encryption.algorithm : "disabled");
    }
}
