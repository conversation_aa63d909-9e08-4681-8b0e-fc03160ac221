package com.tinyzk.user.center.common.util;

import com.tinyzk.user.center.service.BatchProgressNotificationService;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 异步批量处理进度跟踪器
 */
@Slf4j
public class AsyncBatchProgressTracker {

    private final String batchId;
    private final int totalFiles;
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger failureCount = new AtomicInteger(0);
    private final AtomicInteger skippedCount = new AtomicInteger(0);
    private final LocalDateTime startTime;
    private final BatchProgressNotificationService notificationService;

    public AsyncBatchProgressTracker(String batchId, int totalFiles, 
                                   BatchProgressNotificationService notificationService) {
        this.batchId = batchId;
        this.totalFiles = totalFiles;
        this.startTime = LocalDateTime.now();
        this.notificationService = notificationService;
        
        // 初始化进度
        notificationService.initializeProgress(batchId, totalFiles);
    }

    /**
     * 记录文件块处理完成
     */
    public void recordChunkProcessed(int chunkSize, int successCount, int failureCount, int skippedCount) {
        this.processedCount.addAndGet(chunkSize);
        this.successCount.addAndGet(successCount);
        this.failureCount.addAndGet(failureCount);
        this.skippedCount.addAndGet(skippedCount);

        // 通知进度更新
        notificationService.updateBatchProgress(batchId, chunkSize, successCount, failureCount, skippedCount);

        log.info("批量处理进度更新: batchId={}, processed={}/{}, success={}, failure={}, skipped={}",
                batchId, this.processedCount.get(), totalFiles, 
                this.successCount.get(), this.failureCount.get(), this.skippedCount.get());
    }

    /**
     * 记录单个文件处理完成
     */
    public void recordFileProcessed(String fileName, boolean success, boolean skipped) {
        int processed = processedCount.incrementAndGet();
        
        if (skipped) {
            skippedCount.incrementAndGet();
        } else if (success) {
            successCount.incrementAndGet();
        } else {
            failureCount.incrementAndGet();
        }

        // 通知进度更新
        notificationService.updateFileProgress(batchId, fileName, success, skipped, 0);
    }

    /**
     * 获取当前进度百分比
     */
    public double getProgressPercent() {
        return (double) processedCount.get() / totalFiles * 100;
    }

    /**
     * 检查是否已完成
     */
    public boolean isCompleted() {
        return processedCount.get() >= totalFiles;
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        return processedCount.get() > 0 ? 
            (double) successCount.get() / processedCount.get() * 100 : 0;
    }

    // Getters
    public String getBatchId() { return batchId; }
    public int getTotalFiles() { return totalFiles; }
    public int getProcessedCount() { return processedCount.get(); }
    public int getSuccessCount() { return successCount.get(); }
    public int getFailureCount() { return failureCount.get(); }
    public int getSkippedCount() { return skippedCount.get(); }
    public LocalDateTime getStartTime() { return startTime; }
}
