# 高性能批量简历解析配置
spring:
  # 数据库连接池优化
  datasource:
    hikari:
      maximum-pool-size: 50           # 最大连接数
      minimum-idle: 10                # 最小空闲连接
      connection-timeout: 30000       # 连接超时30秒
      idle-timeout: 600000            # 空闲超时10分钟
      max-lifetime: 1800000           # 连接最大生命周期30分钟
      leak-detection-threshold: 60000 # 连接泄漏检测阈值60秒
      
  # 文件上传优化
  servlet:
    multipart:
      max-file-size: 50MB             # 单文件最大50MB
      max-request-size: 500MB         # 请求最大500MB
      file-size-threshold: 2KB        # 文件大小阈值
      location: /tmp/batch-upload     # 临时文件位置

  # Redis连接池优化
  data:
    redis:
      lettuce:
        pool:
          max-active: 20              # 最大连接数
          max-idle: 10                # 最大空闲连接
          min-idle: 5                 # 最小空闲连接
          max-wait: 5000              # 最大等待时间

# 异步任务执行器优化配置
async:
  task:
    executor:
      # 通用线程池
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 500
      keep-alive-seconds: 60
      thread-name-prefix: "AsyncTask-"
      
      # 批量简历解析专用线程池
      batch-resume-parse-executor:
        core-pool-size: 20            # 核心线程数增加到20
        max-pool-size: 100            # 最大线程数增加到100
        queue-capacity: 1000          # 队列容量增加到1000
        keep-alive-seconds: 300       # 线程空闲时间5分钟
        thread-name-prefix: "BatchResumeParser-"
        
      # 文件上传专用线程池
      file-upload-executor:
        core-pool-size: 5
        max-pool-size: 20
        queue-capacity: 200
        keep-alive-seconds: 60
        thread-name-prefix: "FileUpload-"

# 批量简历上传优化配置
batch:
  resume:
    upload:
      # 基础配置
      max-file-count: 100             # 增加到100个文件
      max-file-size-mb: 50            # 单文件50MB
      max-total-size-mb: 2048         # 总大小2GB
      max-concurrency: 20             # 最大并发20
      default-timeout-seconds: 120    # 默认超时2分钟
      max-timeout-seconds: 600        # 最大超时10分钟
      
      # 性能优化配置
      async-mode: true                # 启用异步模式
      response-timeout: 5000          # 快速响应5秒
      progress-check-interval: 1000   # 进度检查间隔1秒
      batch-size: 50                  # 批处理大小
      
      # 重试配置
      retry:
        max-attempts: 3
        initial-delay: 1000
        max-delay: 10000
        multiplier: 2.0
        
      # 缓存配置
      cache:
        enabled: true
        ttl: 3600                     # 缓存1小时
        max-size: 10000               # 最大缓存条目

      # 队列配置
      queue:
        enabled: true
        sub-task-size: 100              # 子任务大小
        max-parallel-sub-tasks: 50      # 最大并行子任务数
        priority-threshold: 10          # 优先级阈值
        dead-letter-ttl: 86400000       # 死信TTL 24小时
        message-ttl: 3600000            # 消息TTL 1小时
        max-retry-attempts: 3           # 最大重试次数

# 第三方API优化配置
resume:
  parse:
    api-url: http://*************:8000
    timeout: 60000                    # 增加到60秒
    max-retries: 3
    
    # 连接池配置
    connection-pool:
      max-total: 200                  # 最大连接数
      max-per-route: 50               # 每个路由最大连接数
      connection-timeout: 10000       # 连接超时10秒
      socket-timeout: 60000           # Socket超时60秒
      connection-request-timeout: 5000 # 连接请求超时5秒
      
    # 限流配置
    rate-limit:
      enabled: true
      permits-per-second: 10          # 每秒10个请求
      burst-capacity: 20              # 突发容量20

# 监控和指标配置
management:
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        "[http.server.requests]": true
        "[batch.resume.processing.time]": true
      percentiles:
        "[http.server.requests]": 0.5, 0.95, 0.99
        "[batch.resume.processing.time]": 0.5, 0.95, 0.99
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}

# 日志配置优化
logging:
  level:
    com.tinyzk.user.center.service.impl.BatchResumeParseServiceImpl: INFO
    com.tinyzk.user.center.service.ThirdPartyResumeParseService: INFO
    org.springframework.web.socket: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"

# WebSocket配置
websocket:
  batch-progress:
    enabled: true
    endpoint: "/ws/batch-progress"
    allowed-origins: "*"
    heartbeat-interval: 30000         # 心跳间隔30秒
    max-sessions: 1000                # 最大会话数

# RabbitMQ消息队列配置
  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:admin}
    password: ${RABBITMQ_PASSWORD:admin123}
    virtual-host: ${RABBITMQ_VIRTUAL_HOST:/}
    connection-timeout: 15000
    requested-heartbeat: 60
    publisher-confirm-type: correlated
    publisher-returns: true

    # 连接池配置
    cache:
      connection:
        mode: connection
        size: 10
      channel:
        size: 50
        checkout-timeout: 5000

    # 监听器配置
    listener:
      simple:
        concurrency: 5                # 初始并发消费者数量
        max-concurrency: 20           # 最大并发消费者数量
        prefetch: 10                  # 预取消息数量
        acknowledge-mode: manual      # 手动确认模式
        default-requeue-rejected: false # 失败消息不重新入队
        idle-event-interval: 30000    # 空闲事件间隔
        retry:
          enabled: true
          initial-interval: 1000      # 初始重试间隔
          max-attempts: 3             # 最大重试次数
          multiplier: 2.0             # 重试间隔倍数
          max-interval: 10000         # 最大重试间隔
      direct:
        consumers-per-queue: 5        # 每个队列的消费者数量
        prefetch: 10
        acknowledge-mode: manual

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    # 建议通过环境变量设置: ALIBABA_CLOUD_ACCESS_KEY_ID, ALIBABA_CLOUD_ACCESS_KEY_SECRET
    # access-key-id: ${ALIBABA_CLOUD_ACCESS_KEY_ID}
    # access-key-secret: ${ALIBABA_CLOUD_ACCESS_KEY_SECRET}
    bucket-name: user-center-files
    base-path: batch-resume/
    enable-https: true
    connection-timeout: 50000
    socket-timeout: 50000
    max-connections: 1024
    max-error-retry: 3
    encryption:
      enabled: true                   # 启用加密
      algorithm: AES256               # 加密算法: AES256, KMS, SM4
      # kms-key-id: your-kms-key-id   # KMS密钥ID (algorithm为KMS时必填)
      # kms-encryption-context: your-context # KMS加密上下文 (可选)
      client-side-enabled: false     # 是否启用客户端加密
      # client-side-key: your-client-key # 客户端加密密钥
      client-side-algorithm: AES/CTR/NoPadding # 客户端加密算法

# 分布式任务配置
mega-batch:
  enabled: true
  sub-task-size: 100                  # 子任务大小100个文件
  max-parallel-sub-tasks: 50          # 最大并行子任务数
  file-storage:
    type: aliyun-oss                  # 文件存储类型改为阿里云OSS
