# 批量简历解析系统部署和运维指南

## 1. 系统架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx LB      │    │  User Center    │    │   Monitoring    │
│   (负载均衡)     │    │   App Cluster   │    │    Stack        │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   SSL/TLS   │ │    │ │   App-1     │ │    │ │ Prometheus  │ │
│ │   限流控制   │ │    │ │   App-2     │ │    │ │   Grafana   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ │   ELK       │ │
└─────────────────┘    └─────────────────┘    │ └─────────────┘ │
         │                       │             └─────────────────┘
         └───────────────────────┼─────────────────────┘
                                 │
    ┌─────────────────────────────┼─────────────────────────────┐
    │                             │                             │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Layer    │    │  Message Queue  │    │  File Storage   │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │   MySQL     │ │    │ │  RabbitMQ   │ │    │ │   MinIO     │ │
│ │   Redis     │ │    │ │   Cluster   │ │    │ │   Cluster   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. 环境要求

### 2.1 硬件要求

**最小配置（开发/测试环境）：**
- CPU: 4核
- 内存: 8GB
- 存储: 100GB SSD
- 网络: 100Mbps

**推荐配置（生产环境）：**
- CPU: 16核
- 内存: 32GB
- 存储: 500GB SSD
- 网络: 1Gbps

**高性能配置（大规模生产环境）：**
- CPU: 32核
- 内存: 64GB
- 存储: 1TB NVMe SSD
- 网络: 10Gbps

### 2.2 软件要求

- Docker 20.10+
- Docker Compose 2.0+
- Java 17+
- MySQL 8.0+
- Redis 7.0+
- RabbitMQ 3.12+

## 3. 部署步骤

### 3.1 快速部署

```bash
# 1. 克隆项目
git clone <repository-url>
cd user_center

# 2. 构建应用镜像
docker build -t user-center:latest .

# 3. 启动所有服务
docker-compose -f docker/docker-compose-performance.yml up -d

# 4. 检查服务状态
docker-compose -f docker/docker-compose-performance.yml ps

# 5. 查看日志
docker-compose -f docker/docker-compose-performance.yml logs -f user-center-app-1
```

### 3.2 数据库初始化

```bash
# 1. 等待MySQL启动
docker-compose -f docker/docker-compose-performance.yml exec mysql mysql -uroot -proot123456 -e "SELECT 1"

# 2. 执行数据库脚本
docker-compose -f docker/docker-compose-performance.yml exec mysql mysql -uuser_center -puser_center123 user_center < sql/batch_resume_performance_optimization.sql

# 3. 验证表创建
docker-compose -f docker/docker-compose-performance.yml exec mysql mysql -uuser_center -puser_center123 user_center -e "SHOW TABLES"
```

### 3.3 配置验证

```bash
# 1. 健康检查
curl http://localhost/health
curl http://localhost/actuator/health

# 2. 监控检查
curl http://localhost:9090  # Prometheus
curl http://localhost:3000  # Grafana (admin/admin123)

# 3. 消息队列检查
curl http://localhost:15672  # RabbitMQ (admin/admin123)
```

## 4. 性能调优

### 4.1 JVM调优

```bash
# 在docker-compose.yml中设置JVM参数
JAVA_OPTS: >
  -Xms4g -Xmx8g
  -XX:+UseG1GC
  -XX:MaxGCPauseMillis=200
  -XX:+UnlockExperimentalVMOptions
  -XX:+UseJVMCICompiler
  -XX:+PrintGCDetails
  -XX:+PrintGCTimeStamps
  -Xloggc:/app/logs/gc.log
```

### 4.2 数据库调优

```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = **********;  -- 4GB
SET GLOBAL innodb_log_file_size = 268435456;      -- 256MB
SET GLOBAL max_connections = 500;
SET GLOBAL thread_cache_size = 50;
SET GLOBAL query_cache_size = 134217728;          -- 128MB
```

### 4.3 Redis调优

```bash
# redis.conf配置
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

## 5. 监控和告警

### 5.1 关键指标监控

**应用指标：**
- 批量任务成功率
- 平均处理时间
- 并发处理数量
- 队列长度
- 错误率

**系统指标：**
- CPU使用率
- 内存使用率
- 磁盘I/O
- 网络流量
- 数据库连接数

**业务指标：**
- 每小时处理文件数
- 第三方API调用成功率
- 用户创建成功率

### 5.2 告警规则

```yaml
# Prometheus告警规则示例
groups:
  - name: batch_processing_alerts
    rules:
      - alert: BatchProcessingHighFailureRate
        expr: rate(batch_task_failed_total[5m]) / rate(batch_task_submitted_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "批量处理失败率过高"
          description: "最近5分钟批量处理失败率超过10%"

      - alert: BatchProcessingHighLatency
        expr: histogram_quantile(0.95, rate(batch_processing_time_bucket[5m])) > 300
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "批量处理延迟过高"
          description: "95%的批量处理请求延迟超过5分钟"
```

## 6. 运维操作

### 6.1 日常维护

```bash
# 1. 查看服务状态
docker-compose -f docker/docker-compose-performance.yml ps

# 2. 重启服务
docker-compose -f docker/docker-compose-performance.yml restart user-center-app-1

# 3. 扩容服务
docker-compose -f docker/docker-compose-performance.yml up -d --scale user-center-app=4

# 4. 查看日志
docker-compose -f docker/docker-compose-performance.yml logs -f --tail=100 user-center-app-1

# 5. 清理历史数据
docker-compose -f docker/docker-compose-performance.yml exec mysql mysql -uuser_center -puser_center123 user_center -e "CALL CleanupBatchProcessingData(30)"
```

### 6.2 备份和恢复

```bash
# 数据库备份
docker-compose -f docker/docker-compose-performance.yml exec mysql mysqldump -uuser_center -puser_center123 user_center > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis备份
docker-compose -f docker/docker-compose-performance.yml exec redis redis-cli BGSAVE

# 文件存储备份
docker-compose -f docker/docker-compose-performance.yml exec minio mc mirror /data /backup
```

### 6.3 故障排查

**常见问题及解决方案：**

1. **内存不足**
   ```bash
   # 检查内存使用
   docker stats
   # 调整JVM堆大小
   # 增加服务器内存
   ```

2. **数据库连接池耗尽**
   ```bash
   # 检查连接数
   docker-compose exec mysql mysql -e "SHOW PROCESSLIST"
   # 调整连接池配置
   # 优化慢查询
   ```

3. **第三方API超时**
   ```bash
   # 检查网络连接
   docker-compose exec user-center-app-1 curl -I http://*************:8000
   # 调整超时配置
   # 增加重试机制
   ```

## 7. 性能测试

### 7.1 运行性能测试

```bash
# 安装依赖
pip install aiohttp asyncio

# 运行测试
python scripts/performance_test.py \
  --url http://localhost \
  --concurrent 20 \
  --files 10 \
  --requests 100 \
  --file-size 200 \
  --timeout 600
```

### 7.2 预期性能指标

**优化后的性能目标：**
- 并发处理能力: 100个文件同时处理
- 单批次处理时间: 2-5分钟（20个文件）
- 系统吞吐量: 2000+份简历/小时
- 成功率: >95%
- P95响应时间: <300秒

## 8. 安全配置

### 8.1 网络安全

```bash
# 防火墙配置
ufw allow 80/tcp
ufw allow 443/tcp
ufw deny 3306/tcp  # 数据库端口仅内网访问
ufw deny 6379/tcp  # Redis端口仅内网访问
```

### 8.2 应用安全

- 启用HTTPS
- 配置JWT密钥
- 限制文件上传大小
- 实施API限流
- 定期更新依赖

## 9. 扩展和优化

### 9.1 水平扩展

```bash
# 增加应用实例
docker-compose -f docker/docker-compose-performance.yml up -d --scale user-center-app=6

# 数据库读写分离
# 配置MySQL主从复制

# Redis集群
# 配置Redis Cluster
```

### 9.2 垂直扩展

- 增加CPU核心数
- 增加内存容量
- 使用更快的存储（NVMe SSD）
- 优化网络带宽

## 10. 联系和支持

如有问题，请联系：
- 技术支持: <EMAIL>
- 运维团队: <EMAIL>
- 紧急联系: +86-xxx-xxxx-xxxx
