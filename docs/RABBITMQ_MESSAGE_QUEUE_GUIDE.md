# RabbitMQ消息队列详细说明

## 1. 架构概览

### 1.1 消息流转架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web请求       │    │   任务分解       │    │   消息生产      │
│                 │    │                 │    │                 │
│ 批量文件上传     │───▶│ 主任务→子任务    │───▶│ 发送到队列       │
│ (1000个文件)    │    │ (100个/子任务)   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    RabbitMQ Exchange                            │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   普通队列       │  │   优先级队列     │  │   死信队列       │ │
│  │ (batch.queue)   │  │ (priority.queue)│  │ (dlq.queue)     │ │
│  │                 │  │                 │  │                 │ │
│  │ TTL: 1小时      │  │ TTL: 30分钟     │  │ 人工处理         │ │
│  │ 重试: 3次       │  │ 重试: 1次       │  │                 │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    消息消费者集群                                │
│                                                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Worker-1      │  │   Worker-2      │  │   Worker-N      │ │
│  │                 │  │                 │  │                 │ │
│  │ 并发: 5-20      │  │ 并发: 5-20      │  │ 并发: 5-20      │ │
│  │ 预取: 10条      │  │ 预取: 10条      │  │ 预取: 10条      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 队列设计

| 队列名称 | 用途 | TTL | 重试次数 | 优先级 | 消费者数量 |
|---------|------|-----|---------|--------|-----------|
| `resume.parse.queue` | 普通批量处理 | 1小时 | 3次 | 无 | 5-20 |
| `resume.parse.priority.queue` | 高优先级处理 | 30分钟 | 1次 | 1-10 | 3-10 |
| `resume.parse.dlq` | 死信处理 | 永久 | 0次 | 无 | 1-3 |

## 2. 消息生产流程

### 2.1 任务分解策略

```java
// 大任务分解为子任务
public void splitAndSendTasks(String mainTaskId, List<String> allFileUrls, 
                             ParseOptions parseOptions, int subTaskSize) {
    
    // 1. 计算子任务数量
    int totalSubTasks = (int) Math.ceil((double) allFileUrls.size() / subTaskSize);
    
    // 2. 分解并发送子任务
    for (int i = 0; i < totalSubTasks; i++) {
        int startIndex = i * subTaskSize;
        int endIndex = Math.min(startIndex + subTaskSize, allFileUrls.size());
        
        List<String> subTaskFiles = allFileUrls.subList(startIndex, endIndex);
        String subTaskId = mainTaskId + "_sub_" + (i + 1);
        
        // 3. 创建子任务消息
        ResumeParseTaskMessage subTaskMessage = ResumeParseTaskMessage.createNormalTask(
                mainTaskId, subTaskId, subTaskFiles, parseOptions);
        
        // 4. 根据文件数量决定队列
        if (subTaskFiles.size() <= 5) {
            messageProducer.sendPriorityParseTask(subTaskMessage, 8); // 高优先级
        } else {
            messageProducer.sendBatchParseTask(subTaskMessage);       // 普通优先级
        }
    }
}
```

### 2.2 消息发送策略

**普通任务发送：**
```java
public void sendBatchParseTask(ResumeParseTaskMessage taskMessage) {
    CorrelationData correlationData = new CorrelationData(UUID.randomUUID().toString());
    
    rabbitTemplate.convertAndSend(
            RabbitMQConfig.RESUME_PARSE_EXCHANGE,
            RabbitMQConfig.RESUME_PARSE_ROUTING_KEY,
            taskMessage,
            message -> {
                MessageProperties properties = message.getMessageProperties();
                properties.setExpiration("3600000"); // 1小时过期
                properties.setHeader("taskId", taskMessage.getTaskId());
                properties.setHeader("retryCount", 0);
                return message;
            },
            correlationData
    );
}
```

**高优先级任务发送：**
```java
public void sendPriorityParseTask(ResumeParseTaskMessage taskMessage, int priority) {
    rabbitTemplate.convertAndSend(
            RabbitMQConfig.RESUME_PARSE_EXCHANGE,
            RabbitMQConfig.RESUME_PARSE_PRIORITY_ROUTING_KEY,
            taskMessage,
            message -> {
                MessageProperties properties = message.getMessageProperties();
                properties.setPriority(priority);    // 设置优先级
                properties.setExpiration("1800000"); // 30分钟过期
                return message;
            },
            correlationData
    );
}
```

## 3. 消息消费流程

### 3.1 普通队列消费

```java
@RabbitListener(queues = RabbitMQConfig.RESUME_PARSE_QUEUE, concurrency = "5-20")
public void processBatchParseTask(
        @Payload ResumeParseTaskMessage taskMessage,
        Message message,
        Channel channel,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
    
    try {
        // 1. 更新任务状态
        progressNotificationService.setTaskStatus(taskMessage.getSubTaskId(), "PROCESSING", null);
        
        // 2. 处理子任务
        processSubTask(taskMessage);
        
        // 3. 手动确认消息
        channel.basicAck(deliveryTag, false);
        
        // 4. 更新完成状态
        progressNotificationService.setTaskStatus(taskMessage.getSubTaskId(), "COMPLETED", null);
        
    } catch (Exception e) {
        handleProcessingError(taskMessage, message, channel, deliveryTag, e);
    }
}
```

### 3.2 错误处理和重试机制

```java
private void handleProcessingError(ResumeParseTaskMessage taskMessage, Message message, 
                                 Channel channel, long deliveryTag, Exception e) {
    try {
        Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get("retryCount");
        if (retryCount == null) retryCount = 0;
        
        if (retryCount < 3) {
            // 重试：更新重试次数并重新入队
            message.getMessageProperties().getHeaders().put("retryCount", retryCount + 1);
            channel.basicNack(deliveryTag, false, true);
            
            log.info("任务重试: taskId={}, retryCount={}", taskMessage.getTaskId(), retryCount + 1);
        } else {
            // 超过重试次数，发送到死信队列
            channel.basicNack(deliveryTag, false, false);
            progressNotificationService.setTaskStatus(taskMessage.getSubTaskId(), "FAILED", e.getMessage());
            
            log.error("任务失败，发送到死信队列: taskId={}", taskMessage.getTaskId());
        }
    } catch (IOException ioException) {
        log.error("消息确认失败: taskId={}", taskMessage.getTaskId(), ioException);
    }
}
```

### 3.3 死信队列处理

```java
@RabbitListener(queues = RabbitMQConfig.RESUME_PARSE_DLQ, concurrency = "1-3")
public void processDeadLetterTask(
        @Payload ResumeParseTaskMessage taskMessage,
        Message message,
        Channel channel,
        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
    
    try {
        // 1. 记录死信任务信息
        recordDeadLetterTask(taskMessage, message);
        
        // 2. 发送告警通知
        notifyAdminForFailedTask(taskMessage, message);
        
        // 3. 确认消息（从死信队列中移除）
        channel.basicAck(deliveryTag, false);
        
        log.warn("死信任务处理完成: taskId={}", taskMessage.getTaskId());
        
    } catch (Exception e) {
        // 死信队列处理失败，直接确认（避免无限循环）
        channel.basicAck(deliveryTag, false);
        log.error("死信任务处理失败: taskId={}", taskMessage.getTaskId(), e);
    }
}
```

## 4. 性能优化策略

### 4.1 消费者并发配置

```yaml
spring:
  rabbitmq:
    listener:
      simple:
        concurrency: 5                # 初始消费者数量
        max-concurrency: 20           # 最大消费者数量
        prefetch: 10                  # 预取消息数量
        acknowledge-mode: manual      # 手动确认模式
```

**配置说明：**
- **concurrency**: 每个队列的初始消费者数量
- **max-concurrency**: 动态扩展的最大消费者数量
- **prefetch**: 每个消费者预取的消息数量，避免消息堆积
- **acknowledge-mode**: 手动确认，确保消息处理完成后才确认

### 4.2 连接池优化

```yaml
spring:
  rabbitmq:
    cache:
      connection:
        mode: connection
        size: 10                      # 连接池大小
      channel:
        size: 50                      # 通道池大小
        checkout-timeout: 5000        # 通道获取超时
```

### 4.3 消息持久化和可靠性

```java
// 队列持久化配置
@Bean
public Queue resumeParseQueue() {
    return QueueBuilder
            .durable(RESUME_PARSE_QUEUE)                    // 队列持久化
            .withArgument("x-message-ttl", 3600000)         // 消息TTL
            .withArgument("x-max-length", 10000)            // 队列最大长度
            .withArgument("x-dead-letter-exchange", DLX)    // 死信交换机
            .build();
}

// 消息持久化发送
rabbitTemplate.convertAndSend(exchange, routingKey, message, msg -> {
    msg.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
    return msg;
});
```

## 5. 监控和管理

### 5.1 队列监控指标

```java
public QueueStatistics getQueueStatistics() {
    QueueStatistics stats = new QueueStatistics();
    
    // 获取队列信息
    QueueInformation queueInfo = rabbitAdmin.getQueueInfo(RESUME_PARSE_QUEUE);
    if (queueInfo != null) {
        stats.setMessageCount(queueInfo.getMessageCount());      // 消息数量
        stats.setConsumerCount(queueInfo.getConsumerCount());    // 消费者数量
    }
    
    return stats;
}
```

### 5.2 健康检查

```java
public QueueHealthStatus getQueueHealthStatus() {
    QueueStatistics stats = getQueueStatistics();
    QueueHealthStatus status = new QueueHealthStatus();
    
    // 检查消息堆积
    if (stats.getMessageCount() > 1000) {
        status.addIssue("队列消息堆积: " + stats.getMessageCount());
    }
    
    // 检查消费者数量
    if (stats.getConsumerCount() == 0) {
        status.addIssue("没有活跃的消费者");
    }
    
    return status;
}
```

## 6. 故障处理

### 6.1 常见问题

**问题1：消息堆积**
```bash
# 检查队列状态
curl -u admin:admin123 http://localhost:15672/api/queues/%2F/resume.parse.queue

# 解决方案：
# 1. 增加消费者数量
# 2. 优化处理逻辑
# 3. 分批处理大任务
```

**问题2：死信队列消息过多**
```bash
# 查看死信队列
curl -u admin:admin123 http://localhost:15672/api/queues/%2F/resume.parse.dlq

# 解决方案：
# 1. 分析失败原因
# 2. 修复问题后重新处理
# 3. 调整重试策略
```

**问题3：消费者连接断开**
```bash
# 检查连接状态
curl -u admin:admin123 http://localhost:15672/api/connections

# 解决方案：
# 1. 检查网络连接
# 2. 调整心跳间隔
# 3. 增加连接超时时间
```

### 6.2 性能调优

**高并发场景优化：**
```yaml
spring:
  rabbitmq:
    listener:
      simple:
        concurrency: 10               # 增加初始消费者
        max-concurrency: 50           # 增加最大消费者
        prefetch: 5                   # 减少预取数量
```

**大消息处理优化：**
```java
// 分片处理大任务
if (taskMessage.getFileUrls().size() > 50) {
    // 进一步分解为更小的子任务
    splitLargeTask(taskMessage);
} else {
    // 直接处理
    processSubTask(taskMessage);
}
```

## 7. 最佳实践

### 7.1 消息设计原则

1. **幂等性**：确保重复处理同一消息不会产生副作用
2. **可序列化**：使用JSON格式，便于调试和跨语言兼容
3. **包含足够信息**：消息应包含处理所需的所有信息
4. **版本兼容**：考虑消息格式的向后兼容性

### 7.2 错误处理策略

1. **分类处理**：区分临时错误和永久错误
2. **指数退避**：重试间隔逐渐增加
3. **死信处理**：失败消息进入死信队列等待人工处理
4. **监控告警**：及时发现和处理异常情况

### 7.3 性能优化建议

1. **合理设置预取数量**：避免消息堆积在消费者端
2. **使用批量确认**：减少网络开销
3. **连接复用**：使用连接池避免频繁创建连接
4. **监控队列深度**：及时发现性能瓶颈
