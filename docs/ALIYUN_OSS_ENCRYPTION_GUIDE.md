# 阿里云OSS加密上传配置指南

## 1. 概述

本系统支持阿里云OSS的多种加密方式，确保简历文件在传输和存储过程中的安全性。

## 2. 加密方式对比

| 加密方式 | 安全级别 | 性能影响 | 管理复杂度 | 推荐场景 |
|---------|---------|---------|-----------|----------|
| **AES256服务端加密** | 高 | 无 | 低 | 一般业务场景 |
| **KMS服务端加密** | 极高 | 极低 | 中 | 高安全要求 |
| **SM4国密加密** | 高 | 低 | 中 | 合规要求 |
| **客户端加密** | 极高 | 中 | 高 | 极高安全要求 |

## 3. 配置说明

### 3.1 基础OSS配置

```yaml
# application-performance.yml
aliyun:
  oss:
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    bucket-name: user-center-files
    base-path: batch-resume/
    enable-https: true
    connection-timeout: 50000
    socket-timeout: 50000
    max-connections: 1024
    max-error-retry: 3
```

### 3.2 AES256服务端加密（推荐）

```yaml
aliyun:
  oss:
    encryption:
      enabled: true
      algorithm: AES256  # OSS完全托管
```

**特点：**
- ✅ 零配置，OSS自动管理密钥
- ✅ 性能无影响
- ✅ 透明加密/解密
- ✅ 符合大多数安全要求

### 3.3 KMS服务端加密（高安全）

```yaml
aliyun:
  oss:
    encryption:
      enabled: true
      algorithm: KMS
      kms-key-id: your-kms-key-id
      kms-encryption-context: "department=hr,project=user-center"
```

**配置步骤：**

1. **创建KMS密钥**
```bash
# 使用阿里云CLI创建KMS密钥
aliyun kms CreateKey --Description "用户中心简历文件加密密钥"
```

2. **设置密钥权限**
```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "RAM": ["acs:ram::your-account-id:role/AliyunOSSRole"]
      },
      "Action": [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:GenerateDataKey"
      ],
      "Resource": "acs:kms:cn-hangzhou:your-account-id:key/your-kms-key-id"
    }
  ]
}
```

### 3.4 SM4国密加密（合规要求）

```yaml
aliyun:
  oss:
    encryption:
      enabled: true
      algorithm: SM4  # 国密算法
```

**适用场景：**
- 政府机构
- 金融行业
- 国密合规要求

### 3.5 客户端加密（极高安全）

```yaml
aliyun:
  oss:
    encryption:
      enabled: true
      client-side-enabled: true
      client-side-algorithm: AES/CTR/NoPadding
      # client-side-key: 通过环境变量设置
```

**环境变量设置：**
```bash
export ALIYUN_OSS_CLIENT_ENCRYPTION_KEY="your-base64-encoded-key"
```

## 4. 安全最佳实践

### 4.1 访问密钥管理

**推荐方式：使用RAM角色**
```bash
# 1. 创建RAM角色
aliyun ram CreateRole --RoleName OSSAccessRole --AssumeRolePolicyDocument '{
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Effect": "Allow",
      "Principal": {
        "Service": ["ecs.aliyuncs.com"]
      }
    }
  ],
  "Version": "1"
}'

# 2. 附加OSS权限策略
aliyun ram AttachPolicyToRole --PolicyType System --PolicyName AliyunOSSFullAccess --RoleName OSSAccessRole
```

**环境变量配置：**
```bash
# 生产环境建议使用环境变量
export ALIBABA_CLOUD_ACCESS_KEY_ID="your-access-key-id"
export ALIBABA_CLOUD_ACCESS_KEY_SECRET="your-access-key-secret"
export ALIBABA_CLOUD_REGION_ID="cn-hangzhou"
```

### 4.2 网络安全

```yaml
aliyun:
  oss:
    enable-https: true  # 强制HTTPS
    endpoint: https://oss-cn-hangzhou.aliyuncs.com  # 使用HTTPS端点
```

### 4.3 访问控制

**Bucket策略示例：**
```json
{
  "Version": "1",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "RAM": ["acs:ram::your-account-id:role/OSSAccessRole"]
      },
      "Action": [
        "oss:PutObject",
        "oss:GetObject",
        "oss:DeleteObject"
      ],
      "Resource": "acs:oss:*:*:user-center-files/batch-resume/*",
      "Condition": {
        "StringEquals": {
          "oss:x-oss-server-side-encryption": "AES256"
        }
      }
    }
  ]
}
```

## 5. 部署配置

### 5.1 Docker环境变量

```bash
# .env文件
ALIBABA_CLOUD_ACCESS_KEY_ID=your-access-key-id
ALIBABA_CLOUD_ACCESS_KEY_SECRET=your-access-key-secret
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
ALIYUN_OSS_BUCKET_NAME=user-center-files
ALIYUN_OSS_ENCRYPTION_ALGORITHM=AES256
ALIYUN_OSS_KMS_KEY_ID=your-kms-key-id
```

### 5.2 Kubernetes配置

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: aliyun-oss-secret
type: Opaque
data:
  access-key-id: <base64-encoded-access-key-id>
  access-key-secret: <base64-encoded-access-key-secret>
  kms-key-id: <base64-encoded-kms-key-id>
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-center-app
spec:
  template:
    spec:
      containers:
      - name: app
        env:
        - name: ALIBABA_CLOUD_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: aliyun-oss-secret
              key: access-key-id
        - name: ALIBABA_CLOUD_ACCESS_KEY_SECRET
          valueFrom:
            secretKeyRef:
              name: aliyun-oss-secret
              key: access-key-secret
```

## 6. 监控和审计

### 6.1 OSS访问日志

```yaml
# 启用OSS访问日志
aliyun:
  oss:
    logging:
      enabled: true
      target-bucket: user-center-logs
      target-prefix: oss-access-logs/
```

### 6.2 KMS密钥使用监控

```bash
# 查看KMS密钥使用情况
aliyun kms DescribeKey --KeyId your-kms-key-id
```

### 6.3 CloudTrail审计

```json
{
  "eventName": "PutObject",
  "eventSource": "oss.aliyuncs.com",
  "requestParameters": {
    "bucketName": "user-center-files",
    "objectName": "batch-resume/task_20241224_001.pdf",
    "serverSideEncryption": "AES256"
  }
}
```

## 7. 故障排查

### 7.1 常见问题

**问题1：加密上传失败**
```bash
# 检查权限
aliyun oss ls oss://user-center-files/batch-resume/ --encryption-type AES256

# 检查KMS权限
aliyun kms Decrypt --CiphertextBlob test-data --KeyId your-kms-key-id
```

**问题2：客户端解密失败**
```java
// 检查密钥格式
String key = System.getenv("ALIYUN_OSS_CLIENT_ENCRYPTION_KEY");
byte[] keyBytes = Base64.getDecoder().decode(key);
SecretKey secretKey = new SecretKeySpec(keyBytes, "AES");
```

### 7.2 性能优化

```yaml
aliyun:
  oss:
    # 连接池优化
    max-connections: 1024
    connection-timeout: 30000
    socket-timeout: 60000
    
    # 分片上传优化
    multipart-upload:
      enabled: true
      part-size: 5242880  # 5MB
      threshold: 10485760  # 10MB
```

## 8. 成本优化

### 8.1 存储类型选择

```yaml
aliyun:
  oss:
    storage-class: IA  # 低频访问存储，适合简历文件
    lifecycle:
      enabled: true
      rules:
        - name: resume-lifecycle
          prefix: batch-resume/
          transitions:
            - days: 30
              storage-class: Archive  # 30天后转为归档存储
```

### 8.2 数据压缩

```java
// 启用压缩上传
ObjectMetadata metadata = new ObjectMetadata();
metadata.setContentEncoding("gzip");
```

## 9. 合规性考虑

### 9.1 数据驻留

```yaml
aliyun:
  oss:
    endpoint: https://oss-cn-beijing.aliyuncs.com  # 选择合规的地域
```

### 9.2 数据保护

- **加密传输**：强制HTTPS
- **加密存储**：AES256/KMS/SM4
- **访问控制**：RAM角色和策略
- **审计日志**：完整的操作记录

## 10. 联系支持

- **技术支持**：<EMAIL>
- **安全咨询**：<EMAIL>
- **阿里云工单**：https://workorder.console.aliyun.com/
