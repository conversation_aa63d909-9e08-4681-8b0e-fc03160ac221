<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阿里云OSS加密批量简历上传</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .file-list {
            margin: 20px 0;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 4px;
            margin: 5px 0;
        }
        .progress-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            transition: width 0.3s ease;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .status-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-card.success { background-color: #d4edda; }
        .status-card.warning { background-color: #fff3cd; }
        .status-card.danger { background-color: #f8d7da; }
        .status-card.info { background-color: #d1ecf1; }
        .encryption-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .log-container {
            max-height: 300px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 阿里云OSS加密批量简历上传</h1>
        
        <!-- 加密信息 -->
        <div class="encryption-info">
            <h3>🛡️ 安全加密特性</h3>
            <ul>
                <li><strong>服务端加密</strong>: 使用AES256算法自动加密存储</li>
                <li><strong>传输加密</strong>: HTTPS安全传输协议</li>
                <li><strong>访问控制</strong>: 基于IAM的精细化权限管理</li>
                <li><strong>数据隔离</strong>: 独立存储桶和路径隔离</li>
            </ul>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-area" id="uploadArea">
            <div>
                <h3>📁 拖拽文件到此处或点击选择</h3>
                <p>支持格式: PDF, DOC, DOCX, TXT</p>
                <p>单文件最大: 50MB | 批量最大: 100个文件</p>
                <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.txt" style="display: none;">
                <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
            </div>
        </div>

        <!-- 文件列表 -->
        <div class="file-list" id="fileList"></div>

        <!-- 解析选项 -->
        <div style="margin: 20px 0;">
            <h3>📋 解析选项</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                <label><input type="checkbox" id="parseBasicInfo" checked> 基本信息</label>
                <label><input type="checkbox" id="parseContactInfo" checked> 联系方式</label>
                <label><input type="checkbox" id="parseEducation" checked> 教育经历</label>
                <label><input type="checkbox" id="parseWorkExperience" checked> 工作经历</label>
                <label><input type="checkbox" id="parseProjectExperience" checked> 项目经历</label>
                <label><input type="checkbox" id="parseSkills" checked> 技能信息</label>
            </div>
        </div>

        <!-- 高级设置 -->
        <div style="margin: 20px 0;">
            <h3>⚙️ 高级设置</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <label>最大并发数:</label>
                    <input type="number" id="maxConcurrency" value="20" min="1" max="50" style="width: 100px;">
                </div>
                <div>
                    <label>超时时间(秒):</label>
                    <input type="number" id="timeoutSeconds" value="120" min="30" max="600" style="width: 100px;">
                </div>
                <div>
                    <label><input type="checkbox" id="overwriteExisting"> 覆盖现有数据</label>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn btn-success" id="uploadBtn" onclick="startUpload()" disabled>
                🚀 开始加密上传
            </button>
            <button class="btn btn-danger" id="cancelBtn" onclick="cancelUpload()" disabled>
                ❌ 取消上传
            </button>
            <button class="btn btn-primary" onclick="clearFiles()">
                🗑️ 清空文件
            </button>
        </div>

        <!-- 进度显示 -->
        <div class="progress-container" id="progressContainer" style="display: none;">
            <h3>📊 处理进度</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%;"></div>
            </div>
            <div id="progressText">准备中...</div>
            
            <div class="status-info" id="statusInfo">
                <div class="status-card info">
                    <h4>总文件数</h4>
                    <div id="totalFiles">0</div>
                </div>
                <div class="status-card success">
                    <h4>成功处理</h4>
                    <div id="successCount">0</div>
                </div>
                <div class="status-card danger">
                    <h4>处理失败</h4>
                    <div id="failureCount">0</div>
                </div>
                <div class="status-card warning">
                    <h4>跳过文件</h4>
                    <div id="skippedCount">0</div>
                </div>
            </div>
        </div>

        <!-- 实时日志 -->
        <div style="margin: 20px 0;">
            <h3>📝 处理日志</h3>
            <div class="log-container" id="logContainer"></div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let currentTaskId = null;
        let progressSocket = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeUploadArea();
            initializeFileInput();
        });

        // 初始化上传区域
        function initializeUploadArea() {
            const uploadArea = document.getElementById('uploadArea');
            
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = Array.from(e.dataTransfer.files);
                addFiles(files);
            });
        }

        // 初始化文件输入
        function initializeFileInput() {
            document.getElementById('fileInput').addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                addFiles(files);
            });
        }

        // 添加文件
        function addFiles(files) {
            files.forEach(file => {
                if (validateFile(file)) {
                    selectedFiles.push(file);
                }
            });
            
            updateFileList();
            updateUploadButton();
        }

        // 验证文件
        function validateFile(file) {
            const allowedTypes = ['application/pdf', 'application/msword', 
                                'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 
                                'text/plain'];
            
            if (!allowedTypes.includes(file.type)) {
                addLog(`❌ 不支持的文件类型: ${file.name}`, 'error');
                return false;
            }
            
            if (file.size > 50 * 1024 * 1024) {
                addLog(`❌ 文件过大: ${file.name} (${formatFileSize(file.size)})`, 'error');
                return false;
            }
            
            return true;
        }

        // 更新文件列表
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>📄 ${file.name} (${formatFileSize(file.size)})</span>
                    <button class="btn btn-danger" onclick="removeFile(${index})">删除</button>
                `;
                fileList.appendChild(fileItem);
            });
        }

        // 移除文件
        function removeFile(index) {
            selectedFiles.splice(index, 1);
            updateFileList();
            updateUploadButton();
        }

        // 清空文件
        function clearFiles() {
            selectedFiles = [];
            updateFileList();
            updateUploadButton();
        }

        // 更新上传按钮状态
        function updateUploadButton() {
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = selectedFiles.length === 0;
        }

        // 开始上传
        async function startUpload() {
            if (selectedFiles.length === 0) {
                alert('请先选择文件');
                return;
            }

            try {
                // 显示进度容器
                document.getElementById('progressContainer').style.display = 'block';
                document.getElementById('uploadBtn').disabled = true;
                document.getElementById('cancelBtn').disabled = false;

                addLog('🚀 开始提交异步批量解析任务...', 'info');

                // 准备表单数据
                const formData = new FormData();
                selectedFiles.forEach(file => {
                    formData.append('files', file);
                });

                // 添加解析选项
                formData.append('parseBasicInfo', document.getElementById('parseBasicInfo').checked);
                formData.append('parseContactInfo', document.getElementById('parseContactInfo').checked);
                formData.append('parseEducation', document.getElementById('parseEducation').checked);
                formData.append('parseWorkExperience', document.getElementById('parseWorkExperience').checked);
                formData.append('parseProjectExperience', document.getElementById('parseProjectExperience').checked);
                formData.append('parseSkills', document.getElementById('parseSkills').checked);
                formData.append('maxConcurrency', document.getElementById('maxConcurrency').value);
                formData.append('timeoutSeconds', document.getElementById('timeoutSeconds').value);
                formData.append('overwriteExisting', document.getElementById('overwriteExisting').checked);

                // 提交任务
                const response = await fetch('/api/admin/async-batch/submit', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    currentTaskId = result.data.taskId;
                    addLog(`✅ 任务提交成功，任务ID: ${currentTaskId}`, 'success');
                    
                    // 开始监控进度
                    startProgressMonitoring();
                } else {
                    throw new Error(result.message);
                }

            } catch (error) {
                addLog(`❌ 任务提交失败: ${error.message}`, 'error');
                resetUploadState();
            }
        }

        // 开始进度监控
        function startProgressMonitoring() {
            if (!currentTaskId) return;

            // 使用WebSocket监控进度
            const wsUrl = `ws://localhost/ws/batch-progress`;
            progressSocket = new WebSocket(wsUrl);

            progressSocket.onopen = function() {
                addLog('🔗 WebSocket连接已建立', 'info');
            };

            progressSocket.onmessage = function(event) {
                const progress = JSON.parse(event.data);
                if (progress.batchId === currentTaskId) {
                    updateProgress(progress);
                }
            };

            progressSocket.onerror = function(error) {
                addLog('❌ WebSocket连接错误，切换到轮询模式', 'warning');
                startPollingProgress();
            };

            progressSocket.onclose = function() {
                addLog('🔌 WebSocket连接已关闭', 'info');
            };

            // 备用轮询机制
            setTimeout(() => {
                if (progressSocket.readyState !== WebSocket.OPEN) {
                    startPollingProgress();
                }
            }, 5000);
        }

        // 轮询进度
        function startPollingProgress() {
            const pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/api/admin/batch-progress/${currentTaskId}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        updateProgress(result.data);
                        
                        if (result.data.status === 'COMPLETED' || result.data.status === 'FAILED') {
                            clearInterval(pollInterval);
                        }
                    }
                } catch (error) {
                    addLog(`❌ 获取进度失败: ${error.message}`, 'error');
                }
            }, 2000);
        }

        // 更新进度显示
        function updateProgress(progress) {
            const progressPercent = progress.progressPercent || 0;
            
            // 更新进度条
            document.getElementById('progressFill').style.width = `${progressPercent}%`;
            document.getElementById('progressText').textContent = 
                `${progress.status} - ${progressPercent.toFixed(1)}% (${progress.processedFiles}/${progress.totalFiles})`;

            // 更新统计信息
            document.getElementById('totalFiles').textContent = progress.totalFiles || 0;
            document.getElementById('successCount').textContent = progress.successCount || 0;
            document.getElementById('failureCount').textContent = progress.failureCount || 0;
            document.getElementById('skippedCount').textContent = progress.skippedCount || 0;

            // 添加日志
            if (progress.currentFileName) {
                addLog(`📄 正在处理: ${progress.currentFileName}`, 'info');
            }

            // 检查是否完成
            if (progress.status === 'COMPLETED') {
                addLog('🎉 批量处理完成！', 'success');
                resetUploadState();
            } else if (progress.status === 'FAILED') {
                addLog(`❌ 批量处理失败: ${progress.errorMessage}`, 'error');
                resetUploadState();
            }
        }

        // 取消上传
        function cancelUpload() {
            if (progressSocket) {
                progressSocket.close();
            }
            
            addLog('⏹️ 用户取消了上传任务', 'warning');
            resetUploadState();
        }

        // 重置上传状态
        function resetUploadState() {
            document.getElementById('uploadBtn').disabled = false;
            document.getElementById('cancelBtn').disabled = true;
            currentTaskId = null;
            
            if (progressSocket) {
                progressSocket.close();
                progressSocket = null;
            }
        }

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            const typeIcon = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌'
            };
            
            logEntry.innerHTML = `[${timestamp}] ${typeIcon[type] || 'ℹ️'} ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
